﻿using MVCApp.Helpers;
using Newtonsoft.Json;
using System;

namespace MVCApp.Models.UChoose
{
    public class UChooseGetPointsResponseModel
    {
        [JsonProperty("access_token")]
        public string Access_Token { get; set; }

        public PointsInfo PointsInfo { get; set; }
    }

    public class PointsInfo
    {
        public int pointsToDate { get; set; }
        public int availablePoints { get; set; }
        public int vestedPoints { get; set; }
        public string ccName { get; set; }
        public string ccType { get; set; }
        public bool ccStatus { get; set; }
        public string earnStart { get; set; }
        public string earnEnd { get; set; }
    }
}





