﻿﻿namespace MVCApp.Models.UChoose
{
    /// <summary>
    /// Enum defining all available redemption types for UChoose rewards
    /// </summary>
    public enum RedemptionType
    {
        /// <summary>
        /// Loan rate discount for both Leases and Consumer Loans
        /// </summary>
        LoanRateDiscount,

        /// <summary>
        /// Safe deposit box fee discount
        /// </summary>
        SafeDepositBox,

        /// <summary>
        /// Stop payment fee discount
        /// </summary>
        StopPayment,

        /// <summary>
        /// CD rate enhancement
        /// </summary>
        CDRate,

        /// <summary>
        /// Mortgage closing cost discount
        /// </summary>
        MortgageClosingCost,

        /// <summary>
        /// Home equity closing cost discount
        /// </summary>
        HomeEquityClosingCost
    }
}
