@{
    ViewData["Title"] = "Run Program Page";
}

<style>
    .repgen-grid {
        display: grid;
        grid-template-columns: auto auto;
        grid-gap: 1rem;
    }

    .rgitem.hidden {
        display: none;
    }
</style>

<script>
    function submitForm(specfile) {
        // set repgen input to the specfile
        document.getElementById('repgen').value = specfile;
        // submit the form
        document.getElementById('symitarForm').submit();
    }

    // TODO: Modify this to only filter by repgen name or description
    function filterRepgens() {
        const items = document.querySelectorAll('.rgitem');
        const searchText = $('#searchbar').val().toLowerCase();

        items.forEach(item => {
            const text = item.innerHTML.toLowerCase();
            if (text.includes(searchText)) {
                item.classList.remove('hidden');
            } else {
                item.classList.add('hidden');
            }
        });
    }
</script>

<div class="container mt-5">
    <div class="row mb-4">
        <div class="col-12 text-center">
            <h1 class="display-4">Welcome</h1>
            <p class="lead">Select a program to run, or search for one in the text box below</p>
        </div>
    </div>
    <div class="row mb-5">
        <input class="form-control" id='searchbar' placeholder='Search for a program' type='text' onkeyup='filterRepgens();'> 
    </div>

    <form name="symitarForm" id="symitarForm" method="post" action='symitar://HTMLView~Action=Post'>
        <div class="repgen-grid">
            @{
                var count = 0;
                foreach (var repgen in ViewBag.Repgens)
                {
                    <div class="rgitem">
                        <a href="#" class="text-decoration-none" onclick="submitForm('@repgen.Specfile'); return false;">
                            @repgen.DisplayName
                        </a>
                        <p class="text-muted">@repgen.Description</p>
                    </div>
                    count++;
                }
            }
        </div>
        <input name="repgen" id="repgen" type="hidden" />
    </form>
</div>
