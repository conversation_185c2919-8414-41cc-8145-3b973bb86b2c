﻿/*
Post-Deployment Script Template                            
--------------------------------------------------------------------------------------
 This file contains SQL statements that will be appended to the build script.        
 Use SQLCMD syntax to include a file in the post-deployment script.            
 Example:      :r .\myfile.sql                                
 Use SQLCMD syntax to reference a variable in the post-deployment script.        
 Example:      :setvar TableName MyTable                            
               SELECT * FROM [$(TableName)]                    
--------------------------------------------------------------------------------------
*/
-- Check if we're on a production/staging server
IF CAST(SERVERPROPERTY('ServerName') AS NVARCHAR(128)) LIKE '%D1ISSQL%'
BEGIN
    DECLARE @loginExists INT
    DECLARE @sql NVARCHAR(MAX)
    DECLARE @loginName NVARCHAR(128) = 'treasuryapp'
    DECLARE @password NVARCHAR(128) = 'letmein' -- Replace with actual password or parameter

    -- Check if the login exists at server level
    SELECT @loginExists = COUNT(*) FROM sys.server_principals WHERE name = @loginName

    -- Create login if it doesn't exist
    IF @loginExists = 0
    BEGIN
        SET @sql = 'CREATE LOGIN ' + QUOTENAME(@loginName) + 
                   ' WITH PASSWORD = ''' + @password + ''', 
                   CHECK_EXPIRATION = OFF, 
                   CHECK_POLICY = OFF'
        
        EXEC sp_executesql @sql
        PRINT 'Login ' + @loginName + ' created successfully.'
    END
    ELSE
    BEGIN
        PRINT 'Login ' + @loginName + ' already exists.'
    END

    -- Check if database user exists, create if it doesn't
    IF NOT EXISTS (SELECT 1 FROM sys.database_principals WHERE name = @loginName)
    BEGIN
        SET @sql = 'CREATE USER ' + QUOTENAME(@loginName) + ' FOR LOGIN ' + QUOTENAME(@loginName)
        EXEC sp_executesql @sql
        
        -- Grant execute permissions on all stored procedures
        GRANT EXECUTE ON SCHEMA::dbo TO treasuryapp
        PRINT 'Database user ' + @loginName + ' created and granted permissions.'
    END
    ELSE
    BEGIN
        PRINT 'Database user ' + @loginName + ' already exists.'
    END
END