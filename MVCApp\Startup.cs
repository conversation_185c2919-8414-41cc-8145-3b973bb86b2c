using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.HttpsPolicy;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using MVCApp.Controllers;
using MVCApp.Helpers;
using MVCApp.Models.UChoose;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EpisysToolsClassLibrary.Data;
using EpisysToolsClassLibrary.Models;
using EpisysToolsClassLibrary.DataBases;
using Serilog;



namespace MVCApp
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddControllersWithViews();
            services.AddScoped<SymXchangeAccountHelper>();
            services.AddScoped<UChooseHelper>();
            services.Configure<UChooseDisplayOptions>(options => Configuration.Bind(nameof(UChooseDisplayOptions), options));
            services.Configure<UChooseRewardsConfig>(options => Configuration.Bind(nameof(UChooseRewardsConfig), options));
            services.AddSingleton<ISqlDataAccess, SqlDataAccess>();
            services.AddSingleton<ITreasuryLog, TreasuryLog>();
            services.AddSession();
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            app.UseDeveloperExceptionPage();
            app.UseHttpsRedirection();
            app.UseStaticFiles();
            app.UseSession();
            app.UseSerilogRequestLogging();
            app.UseRouting();

            app.UseAuthorization();
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllerRoute(
                    name: "default",
                    pattern: "{controller=Home}/{action=Index}/{id?}");
            });
        }
    }
}
