﻿using Microsoft.Extensions.Configuration;
using SymXchangePoweronWSDL;
using System.ServiceModel;
using System.Threading.Tasks;

namespace MVCApp.Helpers
{
    public class SymXchangePoweronHelper
    {
        private readonly IConfiguration _config;
        private PowerOnServiceClient _poweronClient;
        private DeviceInformation _device;
        private string _adminPassword;
        private BasicHttpsBinding _basicHttpsBinding;
        private CredentialsChoice _credChoice;

        public SymXchangePoweronHelper(IConfiguration config)
        {
            _config = config;
            var deviceNumber = _config.GetValue<short>("DeviceNumber");
            var deviceType = _config.GetValue<string>("DeviceType");
            _adminPassword = _config.GetValue<string>("SymXAdminPassword");

            _device = new DeviceInformation()
            {
                DeviceNumber = deviceNumber,
                DeviceType = deviceType
            };

            _basicHttpsBinding = new BasicHttpsBinding()
            {
                MaxBufferSize = **********,
                MaxBufferPoolSize = 524288,
                MaxReceivedMessageSize = **********
            };

            _credChoice = new CredentialsChoice()
            {
                Item = new AdministrativeCredentials()
                {
                    Password = _adminPassword
                }
            };
        }

        public async Task<executePowerOnReturnArrayResponse> RunODPPoweron(string accountNumber)
        {
            var endpointAddress = _config.GetValue<string>("PoweronWSDLEndpoint");

            var endpoint = new EndpointAddress(endpointAddress);

            _poweronClient = new PowerOnServiceClient(_basicHttpsBinding, endpoint);

            UserChr userChar1 = new UserChr();
            userChar1.ID = 1;
            userChar1.Value = accountNumber;

            var request = new PowerOnExecutionRequest()
            {
                Credentials = _credChoice,
                DeviceInformation = _device,
                Header = new ExecutionHeader()
                {
                    MessageID = "ECU.Episys.Tools",
                    RGState = "1"
                },
                Body = new ExecutionRequestBody()
                {
                    File = "E.SYMX.ODP.DATA",
                    RGSession = 1,
                    RGSessionSpecified = true,
                    UserDefinedParameters = new UserDefinedParameters()
                    {
                        RGUserChr = new UserChr[] {
                            userChar1
                        }
                    }
                }
            };
            return await _poweronClient.executePowerOnReturnArrayAsync(request);
        }
    }
}
