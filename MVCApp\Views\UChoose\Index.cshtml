@using System.Net.Http;
@using MVCApp.Models.UChoose;
@using Microsoft.Extensions.Options;
@using MVCApp.Helpers.TagHelpers;
@inject IOptions<UChooseDisplayOptions> DisplayOptions;
@inject IOptions<UChooseRewardsConfig> RewardsConfig;
@model MVCApp.ViewModels.uChoose.UChooseViewModel

<style>
    /* ==========================================================================
    UTILITIES & BASE STYLES
    ========================================================================== */

    .btn:disabled {
        cursor: not-allowed;
        opacity: 0.65;
    }

    .spinner-border-sm {
        width: 1rem;
        height: 1rem;
        border-width: 0.2em;
    }

    /* ==========================================================================
       HEADER STYLES
       ========================================================================== */

    .rewards-header {
        margin-bottom: 2rem;
        color: #343a40;
        text-align: center;
        font-weight: 600;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #dee2e6;
    }

    /* ==========================================================================
       TABLE STYLES
       ========================================================================== */

    /* Table Structure */
    .rewards-table {
        border: 1px solid #dee2e6;
        margin-bottom: 2rem;
    }

    /* Table Header */
    .rewards-table thead {
        background-color: #f8f9fa;
    }

    .rewards-table thead th {
        border-bottom: 2px solid #dee2e6;
        font-weight: 600;
        padding: 1rem 0.75rem;
        color: #495057;
    }

    /* Table Body */
    .rewards-table tbody tr {
        transition: all 0.2s ease;
    }

    .rewards-table tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.03);
    }

    .rewards-table td {
        padding: 1rem 0.75rem;
        vertical-align: middle;
    }

    /* Table Footer */
    .rewards-table tfoot {
        background-color: #f8f9fa;
        font-weight: bold;
    }

    .rewards-table tfoot td {
        border-top: 2px solid #dee2e6;
        padding: 1rem 0.75rem;
    }

    .total-row {
        color: #343a40;
    }

    /* ==========================================================================
       CARD & CONTENT STYLES
       ========================================================================== */

    .card-description {
        font-weight: 600;
        color: #343a40;
        position: relative;
        display: inline-block;
    }

    .card-description:after {
        content: '';
        position: absolute;
        width: 0;
        height: 2px;
        bottom: -2px;
        left: 0;
        background-color: #007bff;
        transition: width 0.3s ease;
    }

    tr:hover .card-description:after {
        width: 100%;
    }

    .card-number {
        display: block;
        font-size: 0.85rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }

    /* Transfer Modal Styles */
    .transfer-source-info {
        background-color: #f8f9fa;
        border-radius: 0.25rem;
        padding: 1rem;
        margin-bottom: 1rem;
        border-left: 4px solid #007bff;
    }

    .transfer-source-info h6 {
        margin-bottom: 0.5rem;
        color: #495057;
    }

    .transfer-source-card {
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .transfer-source-points {
        font-weight: 600;
        color: #28a745;
    }

    /* ==========================================================================
       POINTS BADGE STYLES
       ========================================================================== */

    /* Base Badge Style */
    .points-badge {
        display: inline-block;
        padding: 0.35rem 0.6rem;
        font-size: 0.9rem;
        font-weight: 600;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 6px;
        background-color: #f8f9fa;
        color: #495057;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;
    }

    /* Badge Hover Effect */
    tr:hover .points-badge {
        background-color: #e9ecef;
        transform: scale(1.05);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
    }

    /* Badge Color Variants */
    .pointsToDate .points-badge {
        background-color: #e8f4ff;
        color: #0069d9;
    }

    .availablePoints .points-badge {
        background-color: #e8f5e9;
        color: #28a745;
    }

    .vestedPoints .points-badge {
        background-color: #fff3e0;
        color: #fd7e14;
    }

    /* ==========================================================================
       ACTION BUTTON STYLES
       ========================================================================== */

    /* Button Container */
    .action-buttons {
        display: flex;
        gap: 12px;
        justify-content: flex-start;
        align-items: center;
    }

    /* Base Button Style */
    .action-btn {
        display: inline-flex;
        align-items: center;
        gap: 4px;
        padding: 6px 12px;
        font-size: 0.875rem;
        font-weight: 500;
        text-decoration: none;
        border: 1px solid transparent;
        transition: all 0.15s ease;
        cursor: pointer;
        background: none;
    }

    /* Button States */
    .action-btn:hover {
        text-decoration: underline;
    }

    .action-btn:focus {
        outline: 2px solid #0066cc;
        outline-offset: 2px;
    }

    /* Button Variants */
    .transfer-btn {
        color: #dc3545;
        border-color: #dc3545;
    }

    .transfer-btn:hover {
        background-color: #dc3545;
        color: white;
    }

    .redeem-btn {
        color: #6c757d;
        border-color: #6c757d;
    }

    .redeem-btn:hover {
        background-color: #6c757d;
        color: white;
    }

    .add-btn {
        color: #28a745;
        border-color: #28a745;
    }

    .add-btn:hover {
        background-color: #28a745;
        color: white;
    }

    /* ==========================================================================
       LOADING & HIGHLIGHT STATES
       ========================================================================== */

    .loading-row {
        opacity: 0.6;
        background-color: #f8f9fa !important;
        transition: all 0.3s ease;
    }

    .error-row {
        background-color: rgba(220, 53, 69, 0.1) !important;
    }

    .highlight-update {
        animation: highlight-fade 2s ease;
    }

    @@keyframes highlight-fade {
        0% { background-color: rgba(40, 167, 69, 0.2); }
        100% { background-color: transparent; }
    }

    /* ==========================================================================
       NOTE CREATION WARNING STYLES
       ========================================================================== */
    #noteCreationWarning {
        border-left: 5px solid #ffc107;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }

    #noteCreationWarning h5 {
        color: #856404;
        margin-bottom: 1rem;
    }

    #noteCreationWarning p {
        margin-bottom: 0.75rem;
    }

    #noteCreationWarning hr {
        border-color: #ffeaa7;
        margin: 1rem 0;
    }

    /* ==========================================================================
       RESPONSIVE DESIGN
       ========================================================================== */
    @@media (max-width: 768px) {
        .action-buttons {
            flex-direction: column;
            gap: 5px;
        }

        .action-btn {
            width: 100%;
        }
    }
</style>

@section Scripts {
    <script>
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()

            // Variable to track card updates in progress
            var cardsUpdating = 0;

            // Function to update the total row by summing all card values
            function updateTotalRow() {
                var totalRow = $("tfoot tr.total-row");
                if (totalRow.length === 0) {
                    return; // No total row to update
                }

                // If there are cards still updating, put the total row in loading state
                if (cardsUpdating > 0) {
                    // Add loading state to the total row
                    totalRow.addClass("loading-row");
                    totalRow.find(".pointsToDate .points-badge, .availablePoints .points-badge, .vestedPoints .points-badge").html(
                        '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>'
                    );
                    return; // Don't calculate totals yet
                }

                // Calculate new totals
                var totalPointsToDate = 0;
                var totalAvailablePoints = 0;
                var totalVestedPoints = 0;

                // Sum up all values from the table, skipping rows in loading state
                $("tbody tr").not(".loading-row").each(function() {
                    var pointsToDate = parseInt($(this).find(".pointsToDate .points-badge").text().replace(/,/g, '')) || 0;
                    var availablePoints = parseInt($(this).find(".availablePoints .points-badge").text().replace(/,/g, '')) || 0;
                    var vestedPoints = parseInt($(this).find(".vestedPoints .points-badge").text().replace(/,/g, '')) || 0;

                    totalPointsToDate += pointsToDate;
                    totalAvailablePoints += availablePoints;
                    totalVestedPoints += vestedPoints;
                });

                // Update the total row
                totalRow.removeClass("loading-row");
                totalRow.find(".pointsToDate .points-badge").text(totalPointsToDate.toLocaleString());
                totalRow.find(".availablePoints .points-badge").text(totalAvailablePoints.toLocaleString());
                totalRow.find(".vestedPoints .points-badge").text(totalVestedPoints.toLocaleString());

                // Highlight the total row briefly
                totalRow.addClass("highlight-update");
                setTimeout(function() {
                    totalRow.removeClass("highlight-update");
                }, 2000);
            }

            // Function to update a card row with fresh data
            function updateCardRow(cardType, cardNumber, cardDescription) {
                // Find the row for this card
                var row = $("tr").filter(function() {
                    var rowCardType = $(this).find("td[data-cardType]:first").text();
                    var rowCardNumber = $(this).find("td[data-cardNumber]:eq(1)").text();
                    return rowCardType == cardType && rowCardNumber == cardNumber;
                });

                if (row.length === 0) {
                    console.error("Could not find row for card:", cardType, cardNumber);
                    return;
                }

                // Increment the counter of cards being updated
                cardsUpdating++;

                // Update the total row to show loading state
                updateTotalRow();

                // Add loading state to the row
                row.addClass("loading-row");
                row.find(".action-buttons button").prop("disabled", true);

                // Add loading spinners to the points cells
                row.find(".pointsToDate .points-badge, .availablePoints .points-badge, .vestedPoints .points-badge").html(
                    '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>'
                );

                // Fetch updated card data
                $.ajax({
                    type: "GET",
                    url: '@Url.Action("GetCardData", "UChoose")',
                    data: {
                        cardType: cardType,
                        cardNumber: cardNumber,
                        cardDescription: cardDescription
                    },
                    success: function(cardData) {
                        // Update the points values in the row
                        row.find(".pointsToDate .points-badge").text(cardData.pointsToDate.toLocaleString());
                        row.find(".availablePoints .points-badge").text(cardData.availablePoints.toLocaleString());
                        row.find(".vestedPoints .points-badge").text(cardData.vestedPoints.toLocaleString());

                        // Remove loading state
                        row.removeClass("loading-row");
                        row.find(".action-buttons button").prop("disabled", false);

                        // Highlight the row briefly to indicate it was updated
                        row.addClass("highlight-update");
                        setTimeout(function() {
                            row.removeClass("highlight-update");
                        }, 2000);

                        // Decrement the counter of cards being updated
                        cardsUpdating--;

                        // Update the total row now that this card is done
                        updateTotalRow();
                    },
                    error: function(xhr, status, error) {
                        console.error("Error updating card data:", error);

                        // Remove loading state but indicate error
                        row.removeClass("loading-row").addClass("error-row");
                        row.find(".action-buttons button").prop("disabled", false);

                        // Restore previous values or show error indicator
                        row.find(".pointsToDate .points-badge, .availablePoints .points-badge, .vestedPoints .points-badge").text("Error");

                        // Show error message
                        $("#buttonAlertBad").addClass('show');

                        // Decrement the counter of cards being updated
                        cardsUpdating--;

                        // Update the total row now that this card is done (even with error)
                        updateTotalRow();
                    }
                });
            }
             //Transfer points
            $(".transferPointsBtn").click(function () {
                // Get card data from current row
                var row = $(this).closest('tr');

                // Extract card information from the row
                var cardType = row.find("td[data-cardType]:first").text();
                var cardNumber = row.find("td[data-cardNumber]:eq(1)").text();
                var cardDescription = row.find("td.card-description div.card-description").text();
                var vestedPoints = parseInt(row.find("td.vestedPoints").text().replace(/,/g, ''));

                // Format last 4 digits of card number
                var maskedCardNumber = "••••" + (cardNumber.length > 4 ? cardNumber.slice(-4) : cardNumber);

                // Store the card data in the modal for later use
                $("#transferPointsModal").data("cardType", cardType);
                $("#transferPointsModal").data("cardNumber", cardNumber);
                $("#transferPointsModal").data("cardDescription", cardDescription);
                $("#transferPointsModal").data("vestedPoints", vestedPoints);
                $("#transferPointsModal").data("maskedCardNumber", maskedCardNumber);

                // Update the "Transfer From" section in the modal
                $("#transferSourceCard").text(cardDescription);
                $("#transferSourceCardNumber").text(maskedCardNumber);
                $("#transferSourcePoints").text(vestedPoints.toLocaleString());

                // Populate transfer target dropdown (excluding the source card)
                $("#transferTarget").empty().append('<option value="" selected>Select an Option</option>');
                @foreach(var card in Model.CardPoints)
                {
                    <text>
                    // Skip the source card (can't transfer to same card)
                    if (@card.CardType != cardType || "@card.CardNumber" != cardNumber) {
                        var targetMaskedNumber = "••••" + ("@card.CardNumber".length > 4 ? "@card.CardNumber".slice(-4) : "@card.CardNumber");
                        var optionText = "@card.CardDescription" + " (" + targetMaskedNumber + ")";
                        var $option = $('<option></option>')
                            .val("@card.CardType")
                            .attr("data-cardNumber", "@card.CardNumber")
                            .text(optionText);
                        $("#transferTarget").append($option);
                    }
                    </text>
                }

                // Show the modal
                $('#transferPointsModal').modal('show');
            });

            $("#confirmTransferBtn").click(function () {
                // Get button reference and disable it
                var $btn = $(this);
                $btn.prop('disabled', true);
                $btn.html('<span>Processing</span> <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>');

                // Get the stored card data from the modal
                var cardType = $("#transferPointsModal").data("cardType");
                var cardNumber = $("#transferPointsModal").data("cardNumber");
                var points = $("#pointsToMove").val();

                // Get target card information
                var targetOption = $("#transferTarget option:selected");
                var targetCardType = targetOption.val();
                var targetCardNumber = targetOption.data("account");

                console.log("Target card info:", targetCardType, targetCardNumber);

                // Validate inputs
                if (!targetCardType || targetCardType === "") {
                    alert('Please select a card to transfer points to');
                    // Re-enable button
                    $btn.prop('disabled', false);
                    $btn.html('Confirm Transfer');
                    return;
                }

                if (!points || isNaN(points) || points <= 0) {
                    alert('Please enter a valid number of points to transfer');
                    // Re-enable button
                    $btn.prop('disabled', false);
                    $btn.html('Confirm Transfer');
                    return;
                }

                var vestedPoints = $("#transferPointsModal").data("vestedPoints");
                if (parseInt(points) > vestedPoints) {
                    alert('You cannot transfer more points than you have vested');
                    // Re-enable button
                    $btn.prop('disabled', false);
                    $btn.html('Confirm Transfer');
                    return;
                }

                $.ajax({
                    type: "POST",
                    url: '@Url.Action("TransferPoints","UChoose")',
                    data: {
                        receivingCardType: targetCardType,
                        receivingCardNumber: targetCardNumber,
                        burnCardType: cardType,
                        burnCardNumber: cardNumber,
                        points: points
                    },
                    success: function (response) {
                        if (response != 200) {
                            $("#buttonAlertBad").addClass('show');
                        } else {
                            $("#buttonAlertGood").addClass('show');

                            // Close the modal
                            $('#transferPointsModal').modal('hide');

                            // Get the source and target card information
                            var sourceCardType = cardType;
                            var sourceCardNumber = cardNumber;
                            var sourceCardDescription = $("#transferPointsModal").data("cardDescription");

                            var targetCardType = targetCardType;
                            var targetCardNumber = targetCardNumber;
                            // Extract the card description from the selected option text (everything before the parenthesis)
                            var targetOptionText = $("#transferTarget option:selected").text();
                            var targetCardDescription = targetOptionText.substring(0, targetOptionText.lastIndexOf(" ("));

                            // Update both the source and target card rows
                            updateCardRow(sourceCardType, sourceCardNumber, sourceCardDescription);
                            updateCardRow(targetCardType, targetCardNumber, targetCardDescription);

                            // Update the total row if it exists
                            updateTotalRow();
                        }
                    },
                    error: function(xhr, status, error) {
                        // Re-enable button on error
                        $btn.prop('disabled', false);
                        $btn.html('Confirm Transfer');
                        $("#buttonAlertBad").addClass('show');
                    }
                });
            });

             //Redeem points
            $(".redeemPointsBtn").click(function () {
                var row = $(this).closest('tr');
                var cardType = row.find("td[data-cardType]").text();
                var cardNumber = row.find("td[data-cardNumber]").text();
                var cardDescription = row.find("td.card-description div.card-description").text();
                var vestedPoints = parseInt(row.find("td.vestedPoints").text().replace(/,/g, ''));

                // Format last 4 digits of card number
                var maskedCardNumber = "••••" + (cardNumber.length > 4 ? cardNumber.slice(-4) : cardNumber);

                // Store the card data in the modal for later use
                $("#redeemPointsModal").data("cardType", cardType);
                $("#redeemPointsModal").data("cardNumber", cardNumber);
                $("#redeemPointsModal").data("cardDescription", cardDescription);
                $("#redeemPointsModal").data("vestedPoints", vestedPoints);
                $("#redeemPointsModal").data("maskedCardNumber", maskedCardNumber);

                // Update the "Redeem From" section in the modal
                $("#redeemSourceCard").text(cardDescription);
                $("#redeemSourceCardNumber").text(maskedCardNumber);
                $("#redeemSourcePoints").text(vestedPoints.toLocaleString());

                // Reset form
                resetRedemptionForm();

                // Show the modal
                $('#redeemPointsModal').modal('show');
            });

            // Function to check if user has enough points for redemption
            function checkSufficientPoints() {
                var requiredPoints = parseInt($("#pointsToRedeem").val()) || 0;
                var vestedPoints = $("#redeemPointsModal").data("vestedPoints") || 0;
                var hasSufficientPoints = requiredPoints <= 0 || requiredPoints <= vestedPoints;

                if (!hasSufficientPoints) {
                    $("#insufficientPointsWarning").show();
                    $("#confirmRedeemBtn").prop('disabled', true);
                    return false;
                } else {
                    $("#insufficientPointsWarning").hide();
                    // Enable confirm button only if an option is selected and points are sufficient
                    var isOptionSelected = $("#redemptionOption").val() !== "none" && $("#redemptionOption").val() !== "";
                    $("#confirmRedeemBtn").prop('disabled', !(isOptionSelected && requiredPoints > 0) );
                    return true;
                }
            }

            // Handle redemption option change
            $("#redemptionOption").change(function() {
                var selectedOption = $(this).val();

                // Hide all specific input sections
                $("#defaultPointsInput, #loanRateDiscountInputs, #fixedCostInputs").hide();

                // Reset warning
                $("#insufficientPointsWarning").hide();

                // Set points based on selected option
                setRedemptionPoints(selectedOption);

                // Show the appropriate input section based on the selected option
                switch(selectedOption) {
                    case "LoanRateDiscount":
                        $("#loanRateDiscountInputs").show();
                        break;
                    case "SafeDepositBox":
                    case "StopPayment":
                    case "CDRate":
                    case "MortgageClosingCost":
                    case "HomeEquityClosingCost":
                        $("#fixedCostInputs").show();
                        loadFixedCostDetails(selectedOption);
                        break;
                    default:
                        $("#defaultPointsInput").show();
                        break;
                }
            });

            // Handle loan rate tier selection
            $(".loan-rate-tier").change(function() {
                // Update the points value when a tier is selected
                var pointsRequired = $(this).val();
                $("#pointsToRedeem").val(pointsRequired);
                // Check if user has enough points
                checkSufficientPoints();
            });

            // Function to load fixed cost reward details
            function loadFixedCostDetails(rewardType) {
                // Find the matching fixed cost reward from the configuration
                @foreach (var reward in RewardsConfig.Value.FixedCostRewards)
                {
                        <text>
                        if ("@reward.Type" === rewardType) {
                            $("#fixedCostDescription").text("@reward.Description");
                            $("#fixedCostPoints").text("Points Required: @reward.PointsRequired");
                            $("#pointsToRedeem").val(@reward.PointsRequired);

                            // Check sufficient points after setting the value
                            checkSufficientPoints();
                        }
                        </text>
                }
            }

            // Function to reset the redemption form
            function resetRedemptionForm() {
                $("#redemptionOption").val("none");
                $("#pointsToRedeem").val("");
                $(".loan-rate-tier").prop("checked", false);
                $("#loanSelection").val(""); // Clear loan selection
                $("#defaultPointsInput").show();
                $("#loanRateDiscountInputs, #fixedCostInputs").hide();
                $("#insufficientPointsWarning").hide();
                $("#confirmRedeemBtn").prop('disabled', false);
            }

            function setRedemptionPoints(rewardType) {
                // Default to 0
                var pointsRequired = 0;

                // Set points based on reward type
                @foreach (var reward in RewardsConfig.Value.FixedCostRewards)
                {
                        <text>
                        if ("@reward.Type" === rewardType) {
                            pointsRequired = @reward.PointsRequired;
                        }
                        </text>
                }

                // For loan rate discount, the points will be set when the tier is selected
                if (rewardType === "LoanRateDiscount") {
                    // Default to 0, will be updated when user selects a tier
                    pointsRequired = 0;
                }

                // Set the points value in the hidden field
                $("#pointsToRedeem").val(pointsRequired);
                console.log("Set points to:", pointsRequired, "for reward type:", rewardType);

                // Check sufficient points
                checkSufficientPoints();
            }

            // Add event listener for points input field
            $("#pointsToRedeem").on('input', function() {
                checkSufficientPoints();
            });

            $("#confirmRedeemBtn").click(function () {
                // Get button reference
                var $btn = $(this);

                // Get redemption option
                var selectedOption = $("#redemptionOption option:selected").val();

                // Get the stored card data
                var cardType = $("#redeemPointsModal").data("cardType");
                var cardNumber = $("#redeemPointsModal").data("cardNumber");
                var vestedPoints = $("#redeemPointsModal").data("vestedPoints") || 0;

                // Get points to redeem from hidden field
                var points = $("#pointsToRedeem").val();

                // Get loan ID if this is a loan rate discount
                var loanId = null;
                var accountNumber = null;
                if (selectedOption === "LoanRateDiscount") {
                    loanId = $("#loanSelection").val();
                    accountNumber = $("#loanSelection option:selected").data("accountnumber");
                }

                console.log("Selected option:", selectedOption);
                console.log("Points value:", points);
                console.log("Card type:", cardType);
                console.log("Card number:", cardNumber);
                console.log("Vested points:", vestedPoints);
                console.log("Loan ID:", loanId);

                // Validate inputs
                if(selectedOption === "none" || selectedOption === ""){
                    alert('Please select a redemption option');
                    return;
                } else if (selectedOption === "LoanRateDiscount" && (!loanId || loanId === "")) {
                    alert('Please select a loan for the rate discount');
                    return;
                } else if (!points || points <= 0) {
                    alert('Please enter a valid number of points');
                    return;
                } else if (parseInt(points) > vestedPoints) {
                    // Show insufficient points warning
                    $("#insufficientPointsWarning").show();
                    return;
                } else {
                    // Disable button and show spinner only when proceeding with the request
                    $btn.prop('disabled', true);
                    $btn.html('<span>Processing</span> <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>');

                    // Prepare data for AJAX call
                    var ajaxData = {
                        cardType: cardType,
                        cardNum: cardNumber,
                        points: points,
                        redeemOption: selectedOption
                    };

                    // Add loan ID if this is a loan rate discount
                    if (selectedOption === "LoanRateDiscount" && loanId) {
                        ajaxData.accountNumber = accountNumber;
                        ajaxData.loanId = loanId;
                    }

                    $.ajax({
                        type:"POST",
                        url: '@Url.Action("RedeemPoints", "UChoose")',
                        data: ajaxData,
                        success: function(response){
                            // Re-enable button first
                            $btn.prop('disabled', false);
                            $btn.html('Confirm Redeem');

                            if(response.statusCode != 200){
                                $("#buttonAlertBad").addClass('show');
                            }else{
                                // Points redemption was successful
                                if (response.pointsRedemptionSuccessful) {
                                    // Check if note creation was attempted and failed
                                    if (response.noteCreationSuccessful === false && response.noteCreationError) {
                                        // Show warning about note creation failure
                                        showNoteCreationWarning(response.noteCreationError, response.redemptionType);
                                    } else {
                                        // Complete success
                                        $("#buttonAlertGood").addClass('show');
                                    }

                                    // Close the modal
                                    $('#redeemPointsModal').modal('hide');

                                    // Get the card information
                                    var cardType = $("#redeemPointsModal").data("cardType");
                                    var cardNumber = $("#redeemPointsModal").data("cardNumber");
                                    var cardDescription = $("#redeemPointsModal").data("cardDescription");

                                    // Update the card row
                                    updateCardRow(cardType, cardNumber, cardDescription);

                                    // Update the total row if it exists
                                    updateTotalRow();
                                } else {
                                    // Points redemption failed
                                    $("#buttonAlertBad").addClass('show');
                                }
                            }
                        },
                        error: function(xhr, status, error) {
                            // Re-enable button on error
                            $btn.prop('disabled', false);
                            $btn.html('Confirm Redeem');
                            $("#buttonAlertBad").addClass('show');
                        }
                    });
                }
            });

            // Add Points
            $(".addPointsBtn").click(function () {
                // Get card data from current row
                var row = $(this).closest('tr');
                var cardType = row.find("td[data-cardType]").text();
                var cardNumber = row.find("td[data-cardNumber]").text();
                var cardDescription = row.find("td.card-description div.card-description").text();

                // Format last 4 digits of card number
                var maskedCardNumber = "••••" + (cardNumber.length > 4 ? cardNumber.slice(-4) : cardNumber);

                // Store the card data in the modal for later use
                $("#addPointsModal").data("cardType", cardType);
                $("#addPointsModal").data("cardNumber", cardNumber);
                $("#addPointsModal").data("cardDescription", cardDescription);
                $("#addPointsModal").data("maskedCardNumber", maskedCardNumber);

                // Update the "Add Points To" section in the modal
                $("#addPointsCard").text(cardDescription);
                $("#addPointsCardNumber").text(maskedCardNumber);

                // Show the modal
                $('#addPointsModal').modal('show');
            });

            $("#confirmAddPointsBtn").click(function () {
                // Get button reference and disable it
                var $btn = $(this);
                $btn.prop('disabled', true);
                $btn.html('<span>Processing</span> <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>');

                // Get the stored card data from the modal
                var cardType = $("#addPointsModal").data("cardType");
                var cardNumber = $("#addPointsModal").data("cardNumber");
                var points = $("#pointsToAdd").val();

                // Validate input
                if (!points || isNaN(points) || points < 1) {
                    $("#pointsToAdd").addClass("is-invalid");
                    // Re-enable button
                    $btn.prop('disabled', false);
                    $btn.html('Add Points');
                    return;
                }

                $("#pointsToAdd").removeClass("is-invalid");

                // Call the API to add points
                $.ajax({
                    type: "POST",
                    url: '@Url.Action("AddPoints", "UChoose")',
                    data: {
                        cardType: cardType,
                        cardNumber: cardNumber,
                        points: points
                    },
                    success: function(response) {
                        if (response != 200) {
                            $("#buttonAlertBad").addClass('show');
                        } else {
                            $("#buttonAlertGood").addClass('show');

                            // Close the modal
                            $('#addPointsModal').modal('hide');

                            // Get the card information
                            var cardType = $("#addPointsModal").data("cardType");
                            var cardNumber = $("#addPointsModal").data("cardNumber");
                            var cardDescription = $("#addPointsModal").data("cardDescription");

                            // Update the card row
                            updateCardRow(cardType, cardNumber, cardDescription);

                            // Update the total row if it exists
                            updateTotalRow();
                        }
                    },
                    error: function(xhr, status, error) {
                        // Re-enable button on error
                        $btn.prop('disabled', false);
                        $btn.html('Add Points');
                        $("#buttonAlertBad").addClass('show');
                        $('#addPointsModal').modal('hide');
                    }
                });
            });

            // Function to show note creation warning
            function showNoteCreationWarning(errorMessage, redemptionType) {
                var warningHtml = `
                    <div class="alert alert-warning alert-dismissible fade show" role="alert" id="noteCreationWarning">
                        <h5><strong>Points Redeemed Successfully - Note Creation Failed</strong></h5>
                        <p><strong>Your ${redemptionType} redemption was processed successfully and your points have been deducted.</strong></p>
                        <p>However, we were unable to create the required note in your account records.</p>
                        <p><strong>Error details:</strong> ${errorMessage}</p>
                        <hr>
                        <p class="mb-0">
                            <strong>Action Required:</strong> Please take a screenshot of this message and email it to
                            <a href="mailto:<EMAIL>"><EMAIL></a> with details of what you were doing when this error occurred.
                            Include your account number and the redemption details above.
                        </p>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                `;

                // Remove any existing warning
                $("#noteCreationWarning").remove();

                // Add the warning at the top of the container
                $(".container").prepend(warningHtml);

                // Auto-scroll to the top to ensure user sees the warning
                $('html, body').animate({
                    scrollTop: 0
                }, 500);
            }

            // Reset buttons and forms when modals are hidden
            $('#transferPointsModal').on('hidden.bs.modal', function () {
                // Reset button state
                $("#confirmTransferBtn").prop('disabled', false);
                $("#confirmTransferBtn").html('Confirm Transfer');

                // Clear form fields
                $("#pointsToMove").val("");

                // Clear source information
                $("#transferSourceCard").text("");
                $("#transferSourceCardNumber").text("");
                $("#transferSourcePoints").text("");

                // Clear stored data
                $("#transferPointsModal").removeData("cardType");
                $("#transferPointsModal").removeData("cardNumber");
                $("#transferPointsModal").removeData("cardDescription");
                $("#transferPointsModal").removeData("vestedPoints");
                $("#transferPointsModal").removeData("maskedCardNumber");
            });

            $('#redeemPointsModal').on('hidden.bs.modal', function () {
                // Reset button state
                $("#confirmRedeemBtn").prop('disabled', false);
                $("#confirmRedeemBtn").html('Confirm Redeem');

                // Clear form fields
                $("#pointsToRedeem").val("");
                $("#insufficientPointsWarning").hide();

                // Clear source information
                $("#redeemSourceCard").text("");
                $("#redeemSourceCardNumber").text("");
                $("#redeemSourcePoints").text("");

                // Clear stored data
                $("#redeemPointsModal").removeData("cardType");
                $("#redeemPointsModal").removeData("cardNumber");
                $("#redeemPointsModal").removeData("cardDescription");
                $("#redeemPointsModal").removeData("vestedPoints");
                $("#redeemPointsModal").removeData("maskedCardNumber");
            });

            $('#addPointsModal').on('hidden.bs.modal', function () {
                // Reset button state
                $("#confirmAddPointsBtn").prop('disabled', false);
                $("#confirmAddPointsBtn").html('Add Points');

                // Clear form fields
                $("#pointsToAdd").removeClass("is-invalid");
                $("#pointsToAdd").val("");

                // Clear source information
                $("#addPointsCard").text("");
                $("#addPointsCardNumber").text("");

                // Clear stored data
                $("#addPointsModal").removeData("cardType");
                $("#addPointsModal").removeData("cardNumber");
                $("#addPointsModal").removeData("cardDescription");
                $("#addPointsModal").removeData("maskedCardNumber");
            });
        });
    </script>
}

<div class="alert alert-success alert-dismissible fade" role="alert" id="buttonAlertGood">
    <strong>Success!</strong>
    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
        <span aria-hidden="true">&times;</span>
    </button>
</div>

<div class="alert alert-danger alert-dismissible fade" role="alert" id="buttonAlertBad">
    <strong>Error!</strong> Please contact IT for support.
    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
        <span aria-hidden="true">&times;</span>
    </button>
</div>

<div class="container">
    <h4 class="rewards-header">UChoose Rewards Balance for: @Model.UserName</h4>

    <div class="table-responsive">
        <table class="table rewards-table">
            <thead>
                <tr>
                    <th scope="col" condition=@DisplayOptions.Value.ShowCardDescription>Card</th>
                    <th scope="col" condition=@DisplayOptions.Value.ShowCreditOrDebit>Card Type</th>
                    <th scope="col" condition=@DisplayOptions.Value.ShowCardName>Card Name</th>
                    <th scope="col" condition=@DisplayOptions.Value.ShowCardStatus>Card Status</th>
                    <th scope="col" condition=@DisplayOptions.Value.ShowPointsToDate><span>Points To Date</span></th>
                    <th scope="col" condition=@DisplayOptions.Value.ShowVestedPoints><span data-toggle="tooltip" data-placement="top" title="Points earned may not be available for use immediately">Points Earned</span></th>
                    <th scope="col" condition=@DisplayOptions.Value.ShowAvailablePoints><span data-toggle="tooltip" data-placement="top" title="Points available for use now">Available Points</span></th>
                    <th scope="col" class="text-center">Actions</th>
                </tr>
            </thead>
            <tbody condition="@DisplayOptions.Value.ShowDetails">
                @foreach (var card in Model.CardPoints)
                {
                    // TODO: Use data attributes in the tr instead of using hidden td elements, and change redeemPointsBtn click event to use them
                    <tr>
                        <td data-cardType="@card.CardType" style="display:none">@card.CardType</td>
                        <td data-cardNumber="@card.CardNumber" style="display:none">@card.CardNumber</td>
                        <td condition=@DisplayOptions.Value.ShowCardDescription>
                            <div class="card-description">@card.CardDescription</div>
                            <div class="card-number">••••@(card.CardNumber.Length > 4 ? card.CardNumber.Substring(card.CardNumber.Length - 4) : card.CardNumber)</div>
                        </td>
                        <td condition=@DisplayOptions.Value.ShowCreditOrDebit>@card.CreditOrDebit</td>
                        <td condition=@DisplayOptions.Value.ShowCardName>@card.CardName</td>
                        <td condition=@DisplayOptions.Value.ShowCardStatus>@card.CardStatus</td>
                        <td condition=@DisplayOptions.Value.ShowPointsToDate class="pointsToDate">
                            <span class="points-badge">@card.PointsToDate.ToString("###,###,##0")</span>
                        </td>
                        <td condition=@DisplayOptions.Value.ShowAvailablePoints class="availablePoints">
                            <span class="points-badge">@card.AvailablePoints.ToString("###,###,##0")</span>
                        </td>
                        <td condition=@DisplayOptions.Value.ShowVestedPoints class="vestedPoints">
                            <span class="points-badge">@card.VestedPoints.ToString("###,###,##0")</span>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn action-btn transfer-btn transferPointsBtn">Transfer Points</button>
                                <button class="btn action-btn redeem-btn redeemPointsBtn">Redeem Points</button>
                                <button class="btn action-btn add-btn addPointsBtn">Add Points</button>
                            </div>
                        </td>
                    </tr>
                }
            </tbody>
            <tfoot Condition=@DisplayOptions.Value.ShowTotal>
                <tr class="total-row">
                    <td condition=@DisplayOptions.Value.ShowCardDescription><strong>Total Points</strong></td>
                    <td condition=@DisplayOptions.Value.ShowCreditOrDebit></td>
                    <td condition=@DisplayOptions.Value.ShowCardName></td>
                    <td condition=@DisplayOptions.Value.ShowCardStatus></td>
                    <td condition=@DisplayOptions.Value.ShowPointsToDate class="pointsToDate">
                        <span class="points-badge">@Model.TotalPointsToDate.ToString("###,###,##0")</span>
                    </td>
                    <td condition=@DisplayOptions.Value.ShowAvailablePoints class="availablePoints">
                        <span class="points-badge">@Model.TotalAvailablePoints.ToString("###,###,##0")</span>
                    </td>
                    <td condition=@DisplayOptions.Value.ShowVestedPoints class="vestedPoints">
                        <span class="points-badge">@Model.TotalVestedPoints.ToString("###,###,##0")</span>
                    </td>
                    <td></td>
                </tr>
            </tfoot>
        </table>
    </div>
</div>

<!-- Transfer Points Modal -->
<div class="modal fade" id="transferPointsModal" tabindex="-1" role="dialog" aria-labelledby="transferPointsModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="transferPointsModalLabel">Transfer Points</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- Transfer From Section -->
                <div class="transfer-source-info">
                    <h6>Transfer From:</h6>
                    <div class="transfer-source-card" id="transferSourceCard"></div>
                    <div class="card-number" id="transferSourceCardNumber"></div>
                    <div class="mt-2">Vested Points: <span class="transfer-source-points" id="transferSourcePoints"></span></div>
                </div>

                <form id="transferPointsForm">
                    <div class="form-group">
                        <label for="transferTarget">Transfer To</label>
                        <select class="form-control" id="transferTarget">
                            <option value="" selected>Select an Option</option>
                            <!-- Options will be populated dynamically in JavaScript -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="pointsToMove">Points to Transfer</label>
                        <input type="number" class="form-control" id="pointsToMove" min="1" max="" placeholder="Enter points amount">
                        <small class="form-text text-muted">Enter the number of points you want to transfer.</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" id="confirmTransferBtn" class="btn btn-primary">Confirm Transfer</button>
            </div>
        </div>
    </div>
</div>


<!-- Redeem Points Modal -->
<div class="modal fade" id="redeemPointsModal" tabindex="-1" role="dialog" aria-labelledby="redeemPointsModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="redeemPointsModalLabel">Redeem Points</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- Redeem From Section -->
                <div class="transfer-source-info">
                    <h6>Redeem From:</h6>
                    <div class="transfer-source-card" id="redeemSourceCard"></div>
                    <div class="card-number" id="redeemSourceCardNumber"></div>
                    <div class="mt-2">Vested Points: <span class="transfer-source-points" id="redeemSourcePoints"></span></div>
                </div>

                <form id="redeemPointsForm">
                    <div class="form-group">
                        <label for="redemptionOption">Redemption Option</label>
                        <select class="form-control" id="redemptionOption">
                            <option value="none" selected disabled hidden>Select an Option</option>
                            <option value="LoanRateDiscount">Loan Rate Discount</option>
                            <option value="SafeDepositBox">Safe Deposit Box</option>
                            <option value="StopPayment">Stop Payment</option>
                            <option value="CDRate">CD Rate</option>
                            <option value="MortgageClosingCost">Mortgage Closing Cost</option>
                            <option value="HomeEquityClosingCost">Home Equity Closing Cost</option>
                        </select>
                    </div>
                    <!-- Warning message for insufficient points -->
                    <div id="insufficientPointsWarning" class="alert alert-danger" style="display: none;">
                        <strong>Insufficient Points!</strong> You don't have enough points for this redemption option.
                    </div>
                    <!-- Loan Rate Discount specific inputs (initially hidden) -->
                    <div id="loanRateDiscountInputs" style="display: none;">
                        <div class="form-group">
                            <label for="loanSelection">Select Loan</label>
                            <select class="form-control" id="loanSelection">
                                <option value="" selected>Select a Loan</option>
                                @if (Model.Loans != null)
                                {
                                    @foreach (var loan in Model.Loans)
                                    {
                                        <option value="@loan.Id" data-accountnumber="@loan.AccountNumber">@loan.Id - @loan.Description</option>
                                    }
                                }
                            </select>
                            <small class="form-text text-muted">Select the loan to apply the rate discount to.</small>
                        </div>
                        <div class="form-group">
                            <label>Select Discount Tier</label>
                            @foreach (var tier in RewardsConfig.Value.LoanRateDiscounts)
                            {
                                <div class="form-check">
                                    <input class="form-check-input loan-rate-tier" type="radio" name="loanRateTier"
                                           id="tier@(tier.DiscountPercentage)" value="@tier.PointsRequired"
                                           data-description="@tier.Description">
                                    <label class="form-check-label" for="tier@(tier.DiscountPercentage)">
                                        @tier.Description (@tier.PointsRequired points)
                                    </label>
                                </div>
                            }
                        </div>
                    </div>

                    <!-- Fixed Cost Rewards inputs (initially hidden) -->
                    <div id="fixedCostInputs" style="display: none;">
                        <div class="form-group">
                            <label id="fixedCostLabel">Redemption Details</label>
                            <p id="fixedCostDescription" class="text-muted"></p>
                            <p id="fixedCostPoints" class="font-weight-bold"></p>
                        </div>
                    </div>
                    <input type="hidden" id="pointsToRedeem" name="pointsToRedeem" value="">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" id="confirmRedeemBtn" class="btn btn-primary">Confirm Redeem</button>
            </div>
        </div>
    </div>
</div>

<!-- Add Points Modal -->
<div class="modal fade" id="addPointsModal" tabindex="-1" role="dialog" aria-labelledby="addPointsModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addPointsModalLabel">Add Points</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- Add Points To Section -->
                <div class="transfer-source-info">
                    <h6>Add Points To:</h6>
                    <div class="transfer-source-card" id="addPointsCard"></div>
                    <div class="card-number" id="addPointsCardNumber"></div>
                </div>

                <form id="addPointsForm">
                    <div class="form-group">
                        <label for="pointsToAdd">Points to Add</label>
                        <input type="number" class="form-control" id="pointsToAdd" min="1" required placeholder="Enter points amount">
                        <small class="form-text text-muted">Enter the number of points you want to add.</small>
                        <div class="invalid-feedback">
                            Please enter a valid number of points (minimum 1).
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" id="confirmAddPointsBtn" class="btn btn-success">Add Points</button>
            </div>
        </div>
    </div>
</div>
