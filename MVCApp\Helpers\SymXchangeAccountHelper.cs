using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SymXchangeAccountWSDL;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.ServiceModel;
using System.Threading.Tasks;
using System.Xml;

namespace MVCApp.Helpers
{
    public class SymXchangeAccountHelper
    {
        private readonly IConfiguration _config;
        private readonly ILogger<SymXchangeAccountHelper> _logger;
        private AccountServiceClient _accountClient;
        private DeviceInformation _device;
        private string _adminPassword;
        private BasicHttpsBinding _basicHttpsBinding;
        private CredentialsChoice _credChoice;

        public SymXchangeAccountHelper(IConfiguration config, ILogger<SymXchangeAccountHelper> logger)
        {
            _config = config;
            _logger = logger;
            var deviceNumber = _config.GetValue<short>("DeviceNumber");
            var deviceType = _config.GetValue<string>("DeviceType");
            _adminPassword = _config.GetValue<string>("SymXAdminPassword");

            _logger.LogDebug("Initializing SymXchangeAccountHelper with Device Number: {DeviceNumber}, Device Type: {DeviceType}",
                deviceNumber, deviceType);

            _device = new DeviceInformation()
            {
                DeviceNumber = deviceNumber,
                DeviceType = deviceType
            };

            _basicHttpsBinding = new BasicHttpsBinding()
            {
                MaxBufferSize = **********,
                MaxBufferPoolSize = 524288,
                MaxReceivedMessageSize = **********
            };

            _credChoice = new CredentialsChoice()
            {
                Item = new AdministrativeCredentials()
                {
                    Password = _adminPassword
                }
            };
        }

        public async Task<updateShareByIDResponse> AddOdpToShare(string accountNumber, string shareID, short userId)
        {
            _logger.LogInformation("Adding ODP to share. Account: {AccountNumber}, Share ID: {ShareID}, User ID: {UserId}",
                accountNumber, shareID, userId);

            var endpointAddress = _config.GetValue<string>("AccountWSDLEndpoint");

            var endpoint = new EndpointAddress(endpointAddress);

            _accountClient = new AccountServiceClient(_basicHttpsBinding, endpoint);
            _credChoice.ProcessorUser = userId;
            _credChoice.ProcessorUserSpecified = true;

            var messageId = $"UpdateShareByIDRequest_{DateTime.Now:yyyyMMddHHmmss}";
            var request = new UpdateShareByIDRequest()
            {
                Credentials = _credChoice,
                DeviceInformation = _device,
                MessageId = messageId,
                BranchIdSpecified = false,
                AccountNumber = accountNumber,
                ShareId = shareID,
                ShareUpdateableFields = new ShareUpdateableFields()
                {
                    OverdraftTolerance = 700.00M,
                    OverdraftToleranceSpecified = true
                }
            };

            if (_logger.IsEnabled(LogLevel.Debug))
            {
                try
                {
                    var requestXml = SerializeToXml(request);
                    _logger.LogDebug("AddOdpToShare Request XML for Message ID {MessageId}:\n{RequestXml}",
                        messageId, requestXml);
                }
                catch (Exception serializationEx)
                {
                    _logger.LogWarning(serializationEx, "Failed to serialize request XML for logging");
                }
            }

            var response = await _accountClient.updateShareByIDAsync(request);            

            if (_logger.IsEnabled(LogLevel.Debug))
            {
                try
                {
                    var responseXml = SerializeToXml(response);
                    _logger.LogDebug("AddOdpToShare Response XML for Message ID {MessageId}:\n{ResponseXml}",
                        messageId, responseXml);
                }
                catch (Exception serializationEx)
                {
                    _logger.LogWarning(serializationEx, "Failed to serialize response XML for logging");
                }
            }

            return response;
        }

        public async Task<createLoanNoteResponse> CreateLoanNote(string accountNumber, string loanID, short userId, string noteText)
        {
            _logger.LogInformation("Creating loan note. Account: {AccountNumber}, Loan ID: {LoanID}, User ID: {UserId}",
                accountNumber, loanID, userId);

            // Validate and split note text
            if (string.IsNullOrWhiteSpace(noteText))
            {
                _logger.LogWarning("Note text validation failed: Note text is empty or whitespace");
                throw new ArgumentException("Note text cannot be empty or whitespace", nameof(noteText));
            }

            var noteTextLines = SplitTextIntoLines(noteText);

            _logger.LogInformation("Note text split into {LineCount} lines", noteTextLines.Count);

            var endpointAddress = _config.GetValue<string>("AccountWSDLEndpoint");
            var endpoint = new EndpointAddress(endpointAddress);

            _accountClient = new AccountServiceClient(_basicHttpsBinding, endpoint);
            _credChoice.ProcessorUser = userId;
            _credChoice.ProcessorUserSpecified = true;

            var loanNote = new LoanNoteCreatableFields();
            loanNote.Text = new LoanNoteUpdateableFieldsText[noteTextLines.Count];

            // Create an entry for each note line
            for (int i = 0; i < noteTextLines.Count; i++)
            {
                loanNote.Text[i] = new LoanNoteUpdateableFieldsText()
                {
                    Text = noteTextLines[i],
                    EntryId = (short)(i + 1), // EntryId starts at 1
                    EntryIdSpecified = true
                };
            }

            loanNote.Code = 50;
            loanNote.CodeSpecified = true;
            loanNote.UserId = userId;
            loanNote.UserIdSpecified = true;
            var messageId = $"CreateLoanNoteRequest_{DateTime.Now:yyyyMMddHHmmss}";
            var request = new CreateLoanNoteRequest()
            {
                Credentials = _credChoice,
                DeviceInformation = _device,
                MessageId = messageId,
                BranchIdSpecified = false,
                AccountNumber = accountNumber,
                LoanId = loanID,
                LoanNoteCreatableFields = loanNote
            };

            if (_logger.IsEnabled(LogLevel.Debug))
            {
                try
                {
                    var requestXml = SerializeToXml(request);
                    _logger.LogDebug("CreateLoanNote Request XML for Message ID {MessageId}:\n{RequestXml}",
                        messageId, requestXml);
                }
                catch (Exception serializationEx)
                {
                    _logger.LogWarning(serializationEx, "Failed to serialize request XML for logging");
                }
            }

            var response = await _accountClient.createLoanNoteAsync(request);

            if (_logger.IsEnabled(LogLevel.Debug))
            {
                try
                {
                    var responseXml = SerializeToXml(response);
                    _logger.LogDebug("CreateLoanNote Response XML for Message ID {MessageId}:\n{ResponseXml}",
                        messageId, responseXml);
                }
                catch (Exception serializationEx)
                {
                    _logger.LogWarning(serializationEx, "Failed to serialize response XML for logging");
                }
            }

            return response;
        }

        // TODO: Update the return type and WSDL types to match your actual SOAP service
        // The method names and structure should be similar to CreateLoanNote
        public async Task<object> CreateAccountNote(string accountNumber, short userId, string noteText)
        {
            _logger.LogInformation("Creating account note. Account: {AccountNumber}, User ID: {UserId}",
                accountNumber, userId);

            // Validate and split note text
            if (string.IsNullOrWhiteSpace(noteText))
            {
                _logger.LogWarning("Note text validation failed: Note text is empty or whitespace");
                throw new ArgumentException("Note text cannot be empty or whitespace", nameof(noteText));
            }

            var noteTextLines = SplitTextIntoLines(noteText);

            _logger.LogInformation("Note text split into {LineCount} lines", noteTextLines.Count);

            var endpointAddress = _config.GetValue<string>("AccountWSDLEndpoint");
            var endpoint = new EndpointAddress(endpointAddress);

            _accountClient = new AccountServiceClient(_basicHttpsBinding, endpoint);
            _credChoice.ProcessorUser = userId;
            _credChoice.ProcessorUserSpecified = true;

            var messageId = $"CreateAccountNoteRequest_{DateTime.Now:yyyyMMddHHmmss}";

            
            var accountNote = new NoteCreatableFields();
            accountNote.Text = new NoteUpdateableFieldsText[noteTextLines.Count];

            for (int i = 0; i < noteTextLines.Count; i++)
            {
                accountNote.Text[i] = new NoteUpdateableFieldsText()
                {
                    Text = noteTextLines[i],
                    EntryId = (short)(i + 1),
                    EntryIdSpecified = true
                };
            }

            accountNote.Code = 50;
            accountNote.CodeSpecified = true;
            accountNote.UserId = userId;
            accountNote.UserIdSpecified = true;

            var request = new CreateNoteRequest()
            {
                Credentials = _credChoice,
                DeviceInformation = _device,
                MessageId = messageId,
                BranchIdSpecified = false,
                AccountNumber = accountNumber,
                NoteCreatableFields = accountNote
            };

            var response = await _accountClient.createNoteAsync(request);

            if (_logger.IsEnabled(LogLevel.Debug))
            {
                try
                {
                    var responseXml = SerializeToXml(response);
                    _logger.LogDebug("CreateNote Response XML for Message ID {MessageId}:\n{ResponseXml}",
                        messageId, responseXml);
                }
                catch (Exception serializationEx)
                {
                    _logger.LogWarning(serializationEx, "Failed to serialize response XML for logging");
                }
            }

            return response;
        }

        public async Task<object> CreateShareNote(string accountNumber, string shareID, short userId, string noteText)
        {
            _logger.LogInformation("Creating share note. Account: {AccountNumber}, Share ID: {ShareID}, User ID: {UserId}",
                accountNumber, shareID, userId);

            // Validate and split note text
            if (string.IsNullOrWhiteSpace(noteText))
            {
                _logger.LogWarning("Note text validation failed: Note text is empty or whitespace");
                throw new ArgumentException("Note text cannot be empty or whitespace", nameof(noteText));
            }

            var noteTextLines = SplitTextIntoLines(noteText);

            _logger.LogInformation("Note text split into {LineCount} lines", noteTextLines.Count);

            var endpointAddress = _config.GetValue<string>("AccountWSDLEndpoint");
            var endpoint = new EndpointAddress(endpointAddress);

            _accountClient = new AccountServiceClient(_basicHttpsBinding, endpoint);
            _credChoice.ProcessorUser = userId;
            _credChoice.ProcessorUserSpecified = true;

            var messageId = $"CreateShareNoteRequest_{DateTime.Now:yyyyMMddHHmmss}";

            var shareNote = new ShareNoteCreatableFields();
            shareNote.Text = new ShareNoteUpdateableFieldsText[noteTextLines.Count];

            for (int i = 0; i < noteTextLines.Count; i++)
            {
                shareNote.Text[i] = new ShareNoteUpdateableFieldsText()
                {
                    Text = noteTextLines[i],
                    EntryId = (short)(i + 1),
                    EntryIdSpecified = true
                };
            }

            shareNote.Code = 50;
            shareNote.CodeSpecified = true;
            shareNote.UserId = userId;
            shareNote.UserIdSpecified = true;

            var request = new CreateShareNoteRequest()
            {
                Credentials = _credChoice,
                DeviceInformation = _device,
                MessageId = messageId,
                BranchIdSpecified = false,
                AccountNumber = accountNumber,
                ShareId = shareID,
                ShareNoteCreatableFields = shareNote
            };

            var response = await _accountClient.createShareNoteAsync(request);

            if (_logger.IsEnabled(LogLevel.Debug))
            {
                try
                {
                    var responseXml = SerializeToXml(response);
                    _logger.LogDebug("CreateShareNote Response XML for Message ID {MessageId}:\n{ResponseXml}",
                        messageId, responseXml);
                }
                catch (Exception serializationEx)
                {
                    _logger.LogWarning(serializationEx, "Failed to serialize response XML for logging");
                }
            }

            return response;
        }

        private List<string> SplitTextIntoLines(string noteText)
        {
            const int maxLineLength = 40;
            const int maxLines = 50;
            
            var lines = new List<string>();
            var words = noteText.Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);
            
            // Check for words that are too long
            foreach (var word in words)
            {
                if (word.Length > maxLineLength)
                {
                    _logger.LogWarning("Note text validation failed: Single word exceeds 40 characters. Word: {Word}", word);
                    throw new ArgumentException($"Single word '{word}' exceeds maximum length of {maxLineLength} characters", nameof(noteText));
                }
            }
            
            var currentLine = "";
            
            foreach (var word in words)
            {
                // Check if adding this word would exceed the line length
                var potentialLine = string.IsNullOrEmpty(currentLine) ? word : $"{currentLine} {word}";
                
                if (potentialLine.Length <= maxLineLength)
                {
                    currentLine = potentialLine;
                }
                else
                {
                    // Current line is full, add it to lines and start a new line with the current word
                    if (!string.IsNullOrEmpty(currentLine))
                    {
                        lines.Add(currentLine);
                    }
                    currentLine = word;
                }
            }
            
            // Add the last line if it's not empty
            if (!string.IsNullOrEmpty(currentLine))
            {
                lines.Add(currentLine);
            }
            
            // Check if we exceeded the maximum number of lines
            if (lines.Count > maxLines)
            {
                _logger.LogWarning("Note text validation failed: Text splits into {LineCount} lines, exceeding maximum of {MaxLines}", 
                    lines.Count, maxLines);
                throw new ArgumentException($"Note text splits into {lines.Count} lines, exceeding maximum of {maxLines} lines", nameof(noteText));
            }
            
            // Final validation - ensure no line is empty (shouldn't happen with our logic, but safety check)
            for (int i = 0; i < lines.Count; i++)
            {
                if (string.IsNullOrEmpty(lines[i]))
                {
                    _logger.LogWarning("Note text validation failed: Line {LineNumber} is empty after splitting", i + 1);
                    throw new InvalidOperationException($"Line {i + 1} is empty after text splitting");
                }
            }
            
            return lines;
        }

        public async Task<searchLoanPagedSelectFieldsResponse> GetLoansByAccountNumber(string accountNumber, short userId)
        {
            _logger.LogInformation("Getting loans by account number. Account: {AccountNumber}, User ID: {UserId}",
                accountNumber, userId);

            var endpointAddress = _config.GetValue<string>("AccountWSDLEndpoint");

            var endpoint = new EndpointAddress(endpointAddress);

            _accountClient = new AccountServiceClient(_basicHttpsBinding, endpoint);
            _credChoice.ProcessorUser = userId;
            _credChoice.ProcessorUserSpecified = true;
            var messageId = $"GetLoansRequest_{DateTime.Now:yyyyMMddHHmmss}";
            var request = new LoanSearchPagedSelectFieldsRequest()
            {
                Credentials = _credChoice,
                DeviceInformation = _device,
                MessageId = messageId,
                AccountNumber = accountNumber,
                BranchId = 0,
                BranchIdSpecified = false,
                PagingRequestContext = new PagingRequestContext()
                {
                    NumberOfRecordsToReturn = 50,
                    NumberOfRecordsToReturnSpecified = true,
                    NumberOfRecordsToSkip = 0,
                    NumberOfRecordsToSkipSpecified = true,
                    Token = ""
                },
                SelectableFields = new LoanSingleSelectableFields()
                {
                    IncludeAllLoanFields = false,
                    IncludeAllLoanFieldsSpecified = true,
                    LoanFields = new LoanFields()
                    {
                        Description = true,
                        DescriptionSpecified = true,
                        Id = true,
                        IdSpecified = true
                    }
                },
                Query = "CloseDate<={date:'1900-01-01'}"
            };

            if (_logger.IsEnabled(LogLevel.Debug))
            {
                try
                {
                    var requestXml = SerializeToXml(request);
                    _logger.LogDebug("GetLoansByAccountNumber Request XML for Message ID {MessageId}:\n{RequestXml}",
                        messageId, requestXml);
                }
                catch (Exception serializationEx)
                {
                    _logger.LogWarning(serializationEx, "Failed to serialize request XML for logging");
                }
            }

            var response = await _accountClient.searchLoanPagedSelectFieldsAsync(request);

            if (_logger.IsEnabled(LogLevel.Debug))
            {
                try
                {
                    var responseXml = SerializeToXml(response);
                    _logger.LogDebug("GetLoansByAccountNumber Response XML for Message ID {MessageId}:\n{ResponseXml}",
                        messageId, responseXml);
                }
                catch (Exception serializationEx)
                {
                    _logger.LogWarning(serializationEx, "Failed to serialize response XML for logging");
                }
            }

            _logger.LogInformation("Retrieved {Count} loans for account {AccountNumber}", 
                response?.PagedResponse?.Loan?.Length ?? 0, accountNumber);

            return response;
        }

        private string SerializeToXml<T>(T obj)
        {
            var serializer = new System.Xml.Serialization.XmlSerializer(typeof(T));
            using (var stringWriter = new StringWriter())
            using (var xmlWriter = XmlWriter.Create(stringWriter, new XmlWriterSettings { Indent = true }))
            {
                serializer.Serialize(xmlWriter, obj);
                return stringWriter.ToString();
            }
        }
    }
}
