Specfile=PS.ACW~Account Close Workflow~Workflow for processing account closures.
Specfile=EMA.AR.119~Enhanced Member App Revision~Revision update for the member application.
Specfile=jb.html.flashcash.hold.release~FlashCash Hold Release~Module to release funds on hold in FlashCash.
Specfile=HC.CHECK.ORDER.SYNERGENT~Harland Clarke Check Order~Interface for ordering Harlan<PERSON> Clarke checks.
Specfile=IMM.EXISTING~IMM eSign Library~Library for managing electronic signatures.
Specfile=PDF.7MO.DISC.119~7 Month CD Disclosure~Disclosure form for 7-month certificates of deposit.
Specfile=ACCT.DENIAL.LTR~Account Services Denial Letter~Letter template notifying service denial.
Specfile=PDF.ACCOUNTSNAPSHOT.119~Account Snapshot~Document providing a summary view of an account.
Specfile=ACH.MENU~ACH Menu~User menu for ACH (Automated Clearing House) transactions.
Specfile=conv.ACHPOST.TOTALS~ACH Balancing~Tool to balance ACH posting totals.
Specfile=ADD.CHKNG.LTR~Add Checking Letter~Letter used for adding a new checking account.
Specfile=E.OD.BRO.LN.SERVICE.CODES~Add Lien Service Code~Service code addition for lien-related information.
Specfile=ADD.SVGS.LTR~Add Share Letter~Letter template for adding share accounts.
Specfile=PDF.LM.ADVERSE.ACTION~Adverse Action Form~Form to document adverse credit actions.
Specfile=AFTER.ADDR.CHANGE.LTR~After Address Change Letter~Notification letter following an address update.
Specfile=ALTERNATE.ACCOUNT.SEARCH~Alternate Account Search~Module for performing secondary account searches.
Specfile=PDF.RATECHANGE.119~ARM Rate Change Letter~Letter detailing changes in ARM (Adjustable Rate Mortgage) rates.
Specfile=JK.AURORA.INCENT.CREATE~Aurora Incentive~Tool to create incentives under the Aurora program.
Specfile=AUTO.INS~Automobile Insurance Agreement~Contract for automobile insurance coverage.
Specfile=BL.LOANLINER~Balloon Pymnt Loan Disclosure~Disclosure form for loans with a balloon payment.
Specfile=DS.BUS.7.CHECKDIGIT.CALC~Business Debit Card Check Digit Calc~Utility to calculate check digits for business debit cards.
Specfile=BUS.DEBIT.LTR~Business Debit Card Ordered Letter~Notification letter for ordered business debit cards.
Specfile=BUSINESS.VISA.LTR~Business Visa Intro Letter~Introduction letter for business Visa cards.
Specfile=EZBILL.CANCEL.LTR~Cancel Inactive EZBill Letter~Letter to cancel inactive EZBill accounts.
Specfile=JK.INCENT.AUTOWERKS~Car Sale Rebate~Module for creating car sale rebates.
Specfile=FISERV.CARD.CREDIT~Card Activation - CREDIT~Process for activating credit cards.
Specfile=FISERV.CARD~Card Activation - DEBIT~Process for activating debit cards.
Specfile=PDF.REGE.REVERSAL.119~Card Reversal Letter~Notification letter for card reversal transactions.
Specfile=JK.TRAVEL~Card Travel Notes~Notes and guidelines for card travel use.
Specfile=PDF.FINAL.CREDIT.119~Card Final Credit Letter~Final credit notification for card transactions.
Specfile=PDF.PROVISIONAL.LETTER.119~Card Provisional Letter~Interim letter regarding card issuance.
Specfile=PDF.CC.REBUTTAL.119~Card Rebuttal Letter~Document used to rebut disputes or claims on a card.
Specfile=E.OD.CRD.DISPUTE~Card Write Off~Module for handling card charge-offs.
Specfile=E.OD.CASHIER.CHECK.LOOKUP~Cashier Check Lookup~Tool to search for cashier check details.
Specfile=E.OD.CRD.DISPUTE.REV~Card Write Off Reversal~Process for reversing a card write-off.
Specfile=CD.ADDON.DISCLOSURE~CD Addon Disclosure~Disclosure for additional CD products.
Specfile=DS.CFS.MORTGAGE.LTR~CFS - New Mortgage Letter~Letter template for new mortgage applications.
Specfile=PDF.EPISYSCHGADDRESS.119~Change Address~Form for updating address information.
Specfile=DS.EASYSAVER.OPTOUT~Change Up Opt-Out~Module to opt out of EasySaver programs.
Specfile=E.OD.CLO.ACCT.TRK.85~Closed Account Tracking 85~Tool to track closed accounts (version 85).
Specfile=RD.CP.NOTE.DISPLAY~Collection Notes Display~Interface for displaying collection notes.
Specfile=JK.VIN.COPYABLE~Copy VIN~Utility to copy Vehicle Identification Numbers.
Specfile=E.OD.SEC.COUNTERFEIT~Counterfeit Bill Form~Form for reporting counterfeit currency.
Specfile=NEG.TRACKING.CREATE~Create Negative Acct Tracking~Module to track accounts with negative balances.
Specfile=CC.ADDTL.INFO.LTR~Credit Card Addtl Info Letter~Letter providing additional credit card information.
Specfile=CC.APP~Credit Card Application~Application form for obtaining a credit card.
Specfile=CC.APPROVAL.LTR~Credit Card Approval Letter~Letter notifying credit card approval.
Specfile=CC.INCREASE.LTR~Credit Card Increase Letter~Letter authorizing an increase in credit card limits.
Specfile=CC.MERCH.CREDIT.LTR~Credit Card Merchant Credit Letter~Notification of merchant credit on a credit card.
Specfile=CC.NORESPONSE.LTR~Credit Card No Response Letter~Follow-up letter for missing credit card responses.
Specfile=CC.PAYOFF.LTR~Credit Card Payoff Letter~Letter confirming the payoff of a credit card balance.
Specfile=DS.CC.ENVELOPE.FM~Credit Card Return Envelope Maint~Tool for maintaining return envelopes for credit cards.
Specfile=CCPAY~Credit Card Payment Info~Module to display credit card payment information.
Specfile=E.OD.SEC.CTR~CTR Submission Form~Form for submitting CTR (Currency Transaction Report) data.
Specfile=E.OD.CUNEXUS~Cunexus~Module related to the Cunexus service integration.
Specfile=CUSTODIAL.OVER21.LTR.2010~Custodial Over 21 Letter~Letter notifying custodial accounts for members over 21.
Specfile=JK.DECEASED~Deceased Account Notification~Notification for accounts of deceased members.
Specfile=DECEASED.LN.LTR~Deceased Member Loan Letter~Loan letter for accounts of deceased members.
Specfile=DECEASED.SV.LTR~Deceased Member Savings Letter~Savings letter for accounts of deceased members.
Specfile=PDF.DDSPEC.119~Direct Deposit Specifications Form~Form detailing direct deposit specifications.
Specfile=RD.CP.NOTE.DISPLAY~Display Collection Notes~Display interface for collection note details.
Specfile=DS.DMI.TRACKING~DMI Tracking Creation/Expiration~Tool for tracking DMI creation and expiration.
Specfile=ECM.TRAN~ECM Funds Management~Module for managing ECM funds transactions.
Specfile=E.OD.XDI.ESTMT.LOGIN~eDocs Viewer (XDI)~Login module for the electronic documents viewer.
Specfile=EMA.AR.119~Enhanced Member App Revision~Updated revision for the member application.
Specfile=ESNAPSHOT.DEMAND~eSnapshot~Module providing on-demand snapshots of account data.
Specfile=DS.FLF.LTR~Fast Lane Savings Letter~Letter to promote fast lane savings account options.
Specfile=CUTEK.FEE.MANAGER~Fee Reversal Manager~Tool for managing and reversing fees.
Specfile=DAYOFWK~Find Day of Week for a date~Utility to determine the day of the week from a given date.
Specfile=HOWLONG~Find number of days between dates~Calculator for computing the number of days between two dates.
Specfile=jb.html.flashcash.hold.release~FlashCash Hold Release~Module to release funds previously held in FlashCash.
Specfile=E.SEC.FCOVERRIDE~Flashcash Limit Override~Tool to override preset limits in FlashCash.
Specfile=PDF.FOREIGNCURRENCY.119~Foreign Currency Ordering~Form for ordering foreign currency products.
Specfile=FRAUD.ITEM.LTR~Fraud Collection Letter~Letter for addressing potential fraud cases.
Specfile=NEW.FS.LTR.1~Fresh Start NSF Letter~Notification letter for a fresh start after an NSF event.
Specfile=GETAPR~Get Savings Rate from APY~Utility to calculate savings rate based on APY.
Specfile=HC.CHECK.ORDER.SYNERGENT~Harland Clarke Check Order~Interface for ordering Harland Clarke checks.
Specfile=E.OD.HELP.VIEWER~Help File Viewer~Module for viewing help and support documentation.
Specfile=HSA.QC.7.CHECKDIGIT.CALC~HSA Debit Card Check Digit Calc~Calculator for HSA debit card check digits.
Specfile=ACHSTART~IDS ACH Origination~Module to initiate ACH origination processes.
Specfile=PDF.INSTISSUE.119~Instant Issuance Verification~Verification form for instant card issuance.
Specfile=IRA.CD.DISCLOSURE~IRA CD Disclosure~Disclosure document for IRA CDs.
Specfile=IRA.SAVINGS.TRANSFER~IRA Share Transfer Form~Form for transferring shares within an IRA.
Specfile=IRA.DISCLOSURE~IRA UHYMMS Disclosure~Disclosure for IRA-related products (UHYMMS).
Specfile=CD.BUMP~Jump-Up CD FM & Notice~Notice regarding jump-up CDs and related funds management.
Specfile=PDF.INSURANCE.SETTLE.119~Letter of Guarantee~Guarantee letter for insurance settlements.
Specfile=JK.NEW.PREFACCESS.CREATE~Link Existing Accounts~Module to link and access existing accounts.
Specfile=LOAN.COUPON.LTR~Loan Coupon Letter~Letter containing coupons for loan payments.
Specfile=LOANLINER~Loan Disclosure~Disclosure document for loan details.
Specfile=PDF.LOAN.COUPONS.119~Loan Payment Coupons~Document providing coupons for loan payments.
Specfile=PDF.PAYOFF.LETTER.119~Loan Payoff Letter~Letter confirming the payoff of a loan.
Specfile=E.OD.LEN.PAYOFF.SRC.HTML~Loan Payoff Title Tracking~Tool for tracking loan payoff title changes.
Specfile=JK.INCENT.BLACKFRI.LOAN~Loan Incentive~Module for creating Black Friday loan incentives.
Specfile=E.OD.MEMLOYALTY.LOOKUP~Loyalty Payback Info~Tool for looking up loyalty payback information.
Specfile=MBL.LOAN.INFO~MBL Loan Information~Module that displays information on MBL loans.
Specfile=DS.ELECTRONIC.OPTIONS.TRACKING~Member Opt In Status~Tool for tracking members’ opt-in choices electronically.
Specfile=DS.EDITED.IQ9.2012~Member Share/Loan List~List displaying member shares and loan details.
Specfile=DS.TAX.INFO.WIN~Member Tax Information~Module to provide members’ tax-related information.
Specfile=EMV.119~Member Verification~Verification process for member identity.
Specfile=JK.MEMBER.MINI.STATEMENT~Mini-Statement~Mini-statement for a quick account summary.
Specfile=E.OD.BRO.MISSING.INFO~Missing Information~Module for identifying missing member or account details.
Specfile=RE.PAYOFF.LTR~Mortgage Loan Payoff Letter~Letter confirming the payoff of a mortgage loan.
Specfile=PAPER.MORT.PAYOFF.LTR~Mortgage Payoff Ltr-Paper~Paper version of the mortgage payoff letter.
Specfile=ELECT.MORT.PAYOFF.LTR~Mortgage Payoff Ltr-Electronic~Electronic version of the mortgage payoff letter.
Specfile=DS.MPP.TRACKING~MPP Tracking~Tool for monitoring MPP (Mortgage Payment Plan) status.
Specfile=E.BRPROMO.INCENT.CREATE~Mukwonago New Chk Promo~Module for creating new check promotions in Mukwonago.
Specfile=E.OD.WHEEL.CONTEST.AWARD.POST~Mukwonago Wheel Spin~Module for managing the Mukwonago wheel spin contest awards.
Specfile=NEW.MEMBER.LETTER~New Member Letter~Welcome letter for new members.
Specfile=NEW.PRIME.LTR~New Prime Share Letter~Letter introducing prime share products.
Specfile=NEW.PRIME.CHKNG.LTR~New Prime/Checking Letter~Notification letter for new prime checking accounts.
Specfile=NEW.TTT.INFO.LTR~New TTT Info Ltr~Letter providing information on new TTT products.
Specfile=NSF.1.LTR~NSF Letter #1~Initial notification letter for an NSF (Non-Sufficient Funds) incident.
Specfile=NSF.2.LTR~NSF Letter #2~Follow-up letter for an NSF event.
Specfile=E.OD.ODP.DAYS.NEG.RPT~ODP Due Date~Report module for overdue ODP days.
Specfile=E.OD.LAUNCHER.ODP~ODP Eligibility~Tool for determining eligibility for ODP (Overdraft Protection).
Specfile=jb.verafin.ofac.search~OFAC Verafin Search~Search module for Verafin OFAC compliance.
Specfile=LL.NOTE~Open End Loan Note Disclosure~Disclosure document for open-end loan notes.
Specfile=LL.RATES~Open End Loan Rate Addendum~Addendum detailing rates for open-end loans.
Specfile=DS.WAUK.HSA.TRACKING~Outside HSA Tracking~Module for tracking HSA accounts outside the main system.
Specfile=PM.LN.OUTSTANDINGHOLDS~Outstanding Loan Holds~Tool for managing holds on outstanding loans.
Specfile=PM.OUTSTANDINGAUTHHOLDS~Outstanding Share Holds~Module for tracking outstanding holds on share accounts.
Specfile=OVER.730.LIMIT.LTR~Over $730 Limit Letter~Letter for accounts exceeding a $730 limit.
Specfile=ODPRIV.WELCOME.LTR~Overdraft Privilege Letter~Welcome letter explaining overdraft privileges.
Specfile=POTENTIAL.MEMBER.LTR~Potential Member Letter~Letter targeting prospective new members.
Specfile=PRIOR.YEAR.INT~Prior Year Loan Interest~Document reporting previous year’s loan interest.
Specfile=PRIOR.YEAR.DIV~Prior Year Share Dividends~Statement of dividends from the prior year for share accounts.
Specfile=JK.PROOF.ADDR.BOX~Proof of Address~Module for submitting proof of address documentation.
Specfile=DS.REDFLAG.TRACKING.FM~Red Flag Maintenance~Tool for maintaining and tracking red flag alerts.
Specfile=REPRINT~Reprint a Receipt~Utility for reprinting receipts.
Specfile=JB.HTML.SET.AUDIO.PIN~Reset Audio Pin~Interface to reset the audio PIN for enhanced security.
Specfile=RTN.ITEM~Return Item Letter~Letter notifying a returned item transaction.
Specfile=RTN.ITEM.2~Return Item Letter #2~Second version of the return item notification letter.
Specfile=RETURN.MAIL.LTR~Returned Mail~Letter alerting the customer of returned mail.
Specfile=RTN.SIGNED.DOC.LTR~Return Required Signed Documents Letter~Letter requesting the return of signed documents.
Specfile=RTC~Right to Cancel Disclosure~Disclosure regarding the customer's right to cancel a transaction.
Specfile=PDF.SAFEBOXLEASE.119~Safe Deposit Box Lease~Lease agreement for safe deposit box services.
Specfile=PROJECTIONS~Savings & Loan Projection Charts~Charts projecting savings and loan performance.
Specfile=SCANNER.STANDALONE~Scan ID - MFR / MSR~Standalone utility for scanning identification via MFR/MSR.
Specfile=CERTIFICATE~Share Certificate~Official certificate document for share ownership.
Specfile=CD.RATE.CHANGE~Share Certificate Rate Change~Notice of rate changes applied to share certificates.
Specfile=SHARE.HISTORY.PORTRAIT~Share Transaction History Portrait~Visual representation of share transaction history.
Specfile=SP.LOANLINER~Single Pymnt Loan Disclosure~Disclosure document for single payment loans.
Specfile=RD.SSN.VALIDATE.DEMO~Social Security Number Validation~Demo tool for validating Social Security Numbers.
Specfile=SSN.VERIFY.LTR~Social Security Number Verify Letter~Letter verifying a member's Social Security Number.
Specfile=E.OD.MKT.SPEC.COMM.GRP~Special Community Group Deposit~Module for deposits from special community groups.
Specfile=STARTER.CHECKS.WYCOM.NEW~Starter Checks - New Lasers~Issuance module for new starter checks.
Specfile=STMT.DRIVEL.LTR~Statement Information Letter~Letter providing details about account statements.
Specfile=E.OD.BRO.SUMMER.SKIP~Summer Skip Calculator~Utility for calculating summer skip payments.
Specfile=TIME~Time/Date on Episys~Displays current time and date on the Episys platform.
Specfile=E.OD.TTL.MAINTAIN.PROCESS~Title Processing~Module for managing title processing workflows.
Specfile=E.OD.TOM.CASH.BALANCE~TOM Cash Clearing Assistant~Tool to assist with clearing cash balances in TOM.
Specfile=E.OD.ITM.FEES~TOM Non-member check cash fee~Module detailing fees for non-member check cashing.
Specfile=E.TOM.TRANS.HELPDESK~TOM Transaction Help Desk~Support tool for TOM transaction issues.
Specfile=E.OD.COL.1099A~Tracking Record Creation-1099a~Module for creating tracking records for 1099a filings.
Specfile=JK.1099C~Tracking Record Creation-1099c~Module for generating tracking records for 1099c filings.
Specfile=RD.TRAN.ACT~Transaction Activity~Overview of recent transaction activities.
Specfile=E.OD.LAUNCHER.UCHOOSE~uChoose Rewards~Interface for managing uChoose rewards.
Specfile=STALE.CHECK.FINAL.LTR~Unclaimed Check Letter~Final notice letter for unclaimed checks.
Specfile=JK.PREFACCESS.DELETE~Unlink Accounts~Tool to unlink or remove account preferences.
Specfile=UNSECURED.PAYOFF.LTR~Unsecured Loan Payoff Letter~Letter confirming payoff of an unsecured loan.
Specfile=VEHICLE.LN.PAYOFF.LTR~Vehicle Loan Payoff Letter~Notification of vehicle loan payoff.
Specfile=E.OD.VOD~Verification of Deposit~Module to verify deposit information.
Specfile=RD.CHEXSYSTEMS.WEB~View Qualifile~Interface to view Qualifile details via ChexSystems.
Specfile=GC.APPROVAL~Visa Gold Approval Letter~Approval letter for a Visa Gold card application.
Specfile=PC.APPROVAL~Visa Platinum Approval Letter~Approval letter for a Visa Platinum card.
Specfile=CUTEK.WIRE.SSO~Wire Tracking (Cutek)~Tool for tracking wire transfers within the Cutek system.
Specfile=WHO~WHO-Find a User Number~Utility to search for a user number.
Specfile=TDS.WIRE.TRACKING.AUTOWD~Wire Tracking~Automated tracking module for wire transfers.
