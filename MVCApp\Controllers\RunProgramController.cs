using System;
using System.Collections.Generic;
using System.IO;
using Microsoft.AspNetCore.Mvc;

namespace MVCApp.Controllers
{
    public class RunProgramController : Controller
    {
        
        public IActionResult Index()
        {
            // read from wwwroot/symwin.ini file
            var fileContents = System.IO.File.ReadAllText("wwwroot/symwin.ini");
            // split each line to get the Poweron name and the description. example: Specfile=CUTEK.FEE.MANAGER~Fee Reversal Manager should be split to "CUTEK.FEE.MANAGER" and "Fee Reversal Manager"
            var lines = fileContents.Split(new string[] { "\r\n", "\n" }, StringSplitOptions.None);

            var repgens = new List<Repgen>();

            foreach (var line in lines)
            {
                if (line.StartsWith("Specfile="))
                {
                    var splitLine = line.Split('~');
                    var specfile = splitLine[0].Substring(9);
                    
                    var displayName = splitLine[1];
                    var description = splitLine[2];

                    var repgen = new Repgen {
                        Specfile = specfile,
                        DisplayName = displayName,
                        Description = description
                    };
                    repgens.Add(repgen);
                }
            }

            ViewBag.Repgens = repgens;


            return View();
        }
    }

    public class Repgen
    {
        public string Specfile { get; set; }
        public string DisplayName { get; set; }
        public string Description { get; set; }
    }

}