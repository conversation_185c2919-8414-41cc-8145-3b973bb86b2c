﻿﻿using System.Collections.Generic;

namespace MVCApp.Models.UChoose
{
    /// <summary>
    /// Configuration class for UChoose rewards system
    /// </summary>
    public class UChooseRewardsConfig
    {
        /// <summary>
        /// Loan rate discount tiers configuration
        /// </summary>
        public List<LoanRateDiscountConfig> LoanRateDiscounts { get; set; }

        /// <summary>
        /// Fixed-cost rewards configuration
        /// </summary>
        public List<FixedCostRewardConfig> FixedCostRewards { get; set; }
    }

    /// <summary>
    /// Configuration for loan rate discount tiers
    /// </summary>
    public class LoanRateDiscountConfig
    {
        /// <summary>
        /// The percentage discount on the loan rate (e.g., 0.25 means 0.25%)
        /// </summary>
        public decimal DiscountPercentage { get; set; }

        /// <summary>
        /// Points required for this discount tier
        /// </summary>
        public int PointsRequired { get; set; }

        /// <summary>
        /// Description of the discount tier
        /// </summary>
        public string Description { get; set; }
    }

    /// <summary>
    /// Configuration for fixed-cost rewards
    /// </summary>
    public class FixedCostRewardConfig
    {
        /// <summary>
        /// The type of reward (matches RedemptionType enum)
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// Points required for this reward
        /// </summary>
        public int PointsRequired { get; set; }

        /// <summary>
        /// Description of the reward
        /// </summary>
        public string Description { get; set; }
    }
}
