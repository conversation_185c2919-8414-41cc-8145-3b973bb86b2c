﻿using System.Collections.Generic;

namespace MVCApp.ViewModels.ODP
{
    public class ODPViewModel
    {
        public string AccountNumber { get; set; }
        public string OdpAcctNumFound { get; set; }
        public List<string> OdpAccounts { get; set; }
        public int OdpAcctNumCount { get; set; }
        public List<string> AcctOwners { get; set; }
        public string OdpFoundName { get; set; }
        public string OdpShareId { get; set; }
        public short UserNumber { get; set; }
        public List<string> DqReasons { get; set; }
    }
}
