﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EpisysToolsClassLibrary.DataBases;
using EpisysToolsClassLibrary.Models;

namespace EpisysToolsClassLibrary.Data
{
    public class TreasuryLog : ITreasuryLog
    {
        private readonly ISqlDataAccess _db;
        private const string connectionStringName = "TreasuryDb";

        public TreasuryLog(ISqlDataAccess db)
        {
            _db = db;
        }

        public async Task CreateTreasuryLog(int userId, string symbolNumber, string serialNumber, decimal amount, bool isValid, string error, string payee)
        {
             await _db.SaveData("dbo.spTreasuryCheckLog",
                new { UserId = userId, SymbolNumber = symbolNumber, SerialNumber = serialNumber, Amount = amount, IsValid = isValid, Error = error, Payee = payee },
                connectionStringName,
                true);
        }

        public async Task<List<TreasuryModel>> GetTreasuryLogs()
        {
            var results = await _db.LoadData<TreasuryModel, dynamic>(
                "dbo.spGetTreasuryCheckLogs",
                new { },
                connectionStringName, true);
            return results;
        }
    }
}
