﻿@using EpisysToolsClassLibrary.Models
@using X.PagedList
@using X.PagedList.Mvc.Core


@{
    ViewData["Title"] = "TreasuryLog";
}

@model IPagedList<TreasuryModel>

<h2>Treasury API Logs</h2>
<table class="table">
    <thead>
        <tr>
            <th>
                <a href="@Url.Action("TreasuryLog", new { sortOrder = ViewBag.DateSortParam, currentFilter = ViewBag.CurrentFilter, pageSize = ViewBag.CurrentPageSize })">
                    Date
                </a>
            </th>
            <th>
                <a href="@Url.Action("TreasuryLog", new { sortOrder = ViewBag.UserSortParam, currentFilter = ViewBag.CurrentFilter, pageSize = ViewBag.CurrentPageSize })">
                    User ID
                </a>
            </th>
            <th>Payee</th>
            <th>Symbol</th>
            <th>Serial</th>
            <th>Amount</th>
            <th>
                <a href="@Url.Action("TreasuryLog", new { sortOrder = ViewBag.ValidSortParam, currentFilter = ViewBag.CurrentFilter, pageSize = ViewBag.CurrentPageSize })">
                    Valid?
                </a>
            </th>
            <th>Error</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var log in Model)
        {
            <tr>
                <td>@log.AttemptedAt</td>
                <td>@log.UserId</td>
                <td>@log.Payee</td>
                <td>@log.SymbolNumber</td>
                <td>@log.SerialNumber</td>
                <td>@($"{log.Amount:F2}")</td>
                <td>@(log.IsValid ? "✅" : "❌")</td>
                <td>@log.Error</td>
            </tr>
        }
    </tbody>
</table>

<form method="get" class="form-inline mb-3">
    <input type="text"
           name="searchString"
           value="@ViewBag.CurrentFilter"
           class="form-control mr-2"
           placeholder="Search" />

    <input type="number"
           name="userId"
           class="form-control mr-2"
           placeholder="User ID" />

    <input type="date"
           name="date"
           class="form-control mr-2"
           value="@Context.Request.Query["date"]" />

    <select name="pageSize"
            class="form-control mr-2"
            onchange="this.form.submit()">
        @{
            var sizes = new[] { 5, 10, 25, 50 };
            int currentPageSize = ViewBag.CurrentPageSize ?? 10;
        }
         @foreach (var size in sizes)
    {
            if (size == currentPageSize)
            {
                <option value="@size" selected>@size</option>
            }
            else
            {
                <option value="@size">@size</option>
            }
    }
    </select>

    <button type="submit" class="btn btn-primary">Filter</button>
</form>

<!-- Pagination controls -->
@* @Html.PagedListPager(Model, page => Url.Action("TreasuryLog", new { page })) *@
@Html.PagedListPager(Model, page => Url.Action("TreasuryLog", new {
    page,
    sortOrder = ViewBag.CurrentSort,
    currentFilter = ViewBag.CurrentFilter,
    userId = Context.Request.Query["userId"],
    date = Context.Request.Query["date"],
    pageSize = ViewBag.CurrentPageSize
}))
