﻿using Microsoft.AspNetCore.Razor.TagHelpers;

namespace MVCApp.Helpers.TagHelpers
{
    /// <summary>
    /// Stolen straight from the ASP.NET Core Docs: https://learn.microsoft.com/en-us/aspnet/core/mvc/views/tag-helpers/authoring?view=aspnetcore-7.0#condition-tag-helper
    /// </summary>
    [HtmlTargetElement(Attributes = nameof(Condition))]
    public class ConditionTagHelper : TagHelper
    {
        public bool Condition { get; set; }

        public override void Process(TagHelperContext context, TagHelperOutput output)
        {
            if (!Condition)
            {
                output.SuppressOutput();
            }
        }
    }
}
