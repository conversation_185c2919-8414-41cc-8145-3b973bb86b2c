@{
    ViewData["Title"] = "Pay Calculator";
}

<style>
    @@media print {
        .btn,
        header {
            display: none !important;
        }
        .container {
            width: 100%;
            margin: 0;
            padding: 0;
        }
        body {
            padding: 0;
            margin: 0;
        }
        #env {
            display: none !important;
        }
    }
</style>

<div class="container mt-5">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="text-center">Pay Calculator</h1>
            <button onclick="printPage()" class="btn btn-primary float-left">Print</button>
            <button onclick="clearForm()" class="btn btn-success float-right">Clear Form</button>
        </div>
    </div>

    <form id="payCalculator" class="needs-validation" novalidate>
        <!-- Pay Stub - On the Job Since Jan 1 -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">PAY STUB-ON THE JOB SINCE JAN 1</h5>
            </div>
            <div class="card-body">
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Pay stub YTD Earnings</label>
                    <div class="col-sm-8">
                        <input type="number" class="form-control" id="ytdEarnings" step="0.01" 
                               onchange="calculateYTDAverages()">
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Pay stub Date</label>
                    <div class="col-sm-8">
                        <input type="date" class="form-control" id="payStubDate" 
                               onchange="calculateYTDAverages()">
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Week Number</label>
                    <div class="col-sm-8">
                        <input type="number" class="form-control" id="weekNum" readonly>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Weekly Average</label>
                    <div class="col-sm-8">
                        <input type="number" class="form-control" id="weeklyAverage" readonly>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Bi-weekly Average</label>
                    <div class="col-sm-8">
                        <input type="number" class="form-control" id="biWeeklyAverage" readonly>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Annualized</label>
                    <div class="col-sm-8">
                        <input type="number" class="form-control" id="annualized" readonly>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pay Stub - Started Job After Jan 1 -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">PAY STUB-STARTED JOB AFTER JAN 1</h5>
            </div>
            <div class="card-body">
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Pay stub #1</label>
                    <div class="col-sm-8">
                        <input type="number" class="form-control" id="payStub1" step="0.01" 
                               onchange="calculateStubAverage()">
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Pay stub #2</label>
                    <div class="col-sm-8">
                        <input type="number" class="form-control" id="payStub2" step="0.01" 
                               onchange="calculateStubAverage()">
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Average Pay</label>
                    <div class="col-sm-8">
                        <input type="number" class="form-control" id="stubAverage" readonly>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bi-Weekly Direct Deposits -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">BI-WEEKLY DIRECT DEPOSITS</h5>
            </div>
            <div class="card-body">
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Direct Deposit #1</label>
                    <div class="col-sm-8">
                        <input type="number" class="form-control" id="biWeeklyDD1" step="0.01" 
                               onchange="calculateBiWeeklyAverage()">
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Direct Deposit #2</label>
                    <div class="col-sm-8">
                        <input type="number" class="form-control" id="biWeeklyDD2" step="0.01" 
                               onchange="calculateBiWeeklyAverage()">
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Average Bi-weekly Pay</label>
                    <div class="col-sm-8">
                        <input type="number" class="form-control" id="biWeeklyDDAverage" readonly>
                    </div>
                </div>
            </div>
        </div>

        <!-- Weekly Direct Deposits -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">WEEKLY DIRECT DEPOSITS</h5>
            </div>
            <div class="card-body">
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Direct Deposit #1</label>
                    <div class="col-sm-8">
                        <input type="number" class="form-control" id="weeklyDD1" step="0.01" 
                               onchange="calculateWeeklyAverage()">
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Direct Deposit #2</label>
                    <div class="col-sm-8">
                        <input type="number" class="form-control" id="weeklyDD2" step="0.01" 
                               onchange="calculateWeeklyAverage()">
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Direct Deposit #3</label>
                    <div class="col-sm-8">
                        <input type="number" class="form-control" id="weeklyDD3" step="0.01" 
                               onchange="calculateWeeklyAverage()">
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Direct Deposit #4</label>
                    <div class="col-sm-8">
                        <input type="number" class="form-control" id="weeklyDD4" step="0.01" 
                               onchange="calculateWeeklyAverage()">
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Average Weekly Pay</label>
                    <div class="col-sm-8">
                        <input type="number" class="form-control" id="weeklyDDAverage" readonly>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <div class="alert alert-warning">
        <p class="mb-1">*Social Security, pension, bi-monthly and semi-monthly income should NOT be used with this form. Manual calculations are required.</p>
        <p class="mb-0">*Self-employment income should still be calculated with the other form found on the Intranet.</p>
    </div>
</div>

<script>
function calculateYTDAverages() {
    const ytdEarnings = parseFloat(document.getElementById('ytdEarnings').value) || 0;
    const payStubDate = new Date(document.getElementById('payStubDate').value);
    
    if (ytdEarnings && payStubDate) {
        const startOfYear = new Date(payStubDate.getFullYear(), 0, 1);
        const weekNum = Math.ceil((payStubDate - startOfYear) / (7 * 24 * 60 * 60 * 1000));
        
        document.getElementById('weekNum').value = weekNum;
        const weeklyAvg = ytdEarnings / weekNum;
        document.getElementById('weeklyAverage').value = weeklyAvg.toFixed(2);
        document.getElementById('biWeeklyAverage').value = (weeklyAvg * 2).toFixed(2);
        document.getElementById('annualized').value = (weeklyAvg * 52).toFixed(2);
    }
}

function calculateStubAverage() {
    const stub1 = parseFloat(document.getElementById('payStub1').value) || 0;
    const stub2 = parseFloat(document.getElementById('payStub2').value) || 0;
    
    if (stub1 || stub2) {
        const avg = (stub1 + stub2) / 2;
        document.getElementById('stubAverage').value = avg.toFixed(2);
    }
}

function calculateBiWeeklyAverage() {
    const dd1 = parseFloat(document.getElementById('biWeeklyDD1').value) || 0;
    const dd2 = parseFloat(document.getElementById('biWeeklyDD2').value) || 0;
    
    if (dd1 || dd2) {
        const avg = ((dd1 + dd2) / 2) * 1.2; // Multiply by 1.2 for 120%
        document.getElementById('biWeeklyDDAverage').value = avg.toFixed(2);
    }
}

function calculateWeeklyAverage() {
    const dd1 = parseFloat(document.getElementById('weeklyDD1').value) || 0;
    const dd2 = parseFloat(document.getElementById('weeklyDD2').value) || 0;
    const dd3 = parseFloat(document.getElementById('weeklyDD3').value) || 0;
    const dd4 = parseFloat(document.getElementById('weeklyDD4').value) || 0;
    
    const values = [dd1, dd2, dd3, dd4].filter(v => v > 0);
    if (values.length > 0) {
        const avg = (values.reduce((a, b) => a + b) / values.length) * 1.2; // Multiply by 1.2 for 120%
        document.getElementById('weeklyDDAverage').value = avg.toFixed(2);
    }
}

function clearForm() {
    document.getElementById('payCalculator').reset();
    const readonlyInputs = document.querySelectorAll('input[readonly]');
    readonlyInputs.forEach(input => input.value = '');
}

function printPage() {
    window.print();
}
</script>