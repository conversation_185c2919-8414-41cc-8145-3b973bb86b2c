{"Serilog": {"Using": [], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "Enrich": ["FromLogContext", "WithMachineName", "WithProcessId", "WtihThreadId"], "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "C:\\Demos\\Logs\\log.txt", "outputTemplate": "{Timestamp:G}{Message}{NewLine:1}{Exception:1}"}}, {"Name": "File", "Args": {"path": "C:\\Demos\\Logs\\log.json", "formatter": "Serilog.Formatting.Json.Json<PERSON>, Ser<PERSON>g"}}]}, "AllowedHosts": "*", "PoweronWSDLEndpoint": "https://symitar:64800/SymXchange/2021.01/poweron", "AccountWSDLEndpoint": "https://symitar:64800/SymXchange/2021.01/account", "DeviceNumber": "20119", "DeviceType": "ECUDEV", "AppKey": "173495AF-BB12-4712-8027-FE500ED5CEC5", "uChooseApiSettings": {"CertPath": "W:\\ITDEPT\\Information Systems\\Development\\Uchoose Points\\EDCWSITE_API.cert.pfx", "ApiUrl": "https://card.api.fiservapps.com/cs", "FapiId": "********", "ApplicationName": "Card Activation", "AuditId": "123456", "CreditId": "********", "DebitId": "********", "CreditVendorName": "EDC8", "DebitVendorName": "EDCW", "UseMultiAuth": false}, "UChooseRewardsConfig": {"LoanRateDiscounts": [{"DiscountPercentage": 0.25, "PointsRequired": 12500, "Description": ".25% Rate Discount"}, {"DiscountPercentage": 0.5, "PointsRequired": 19500, "Description": ".50% Rate Discount"}, {"DiscountPercentage": 0.75, "PointsRequired": 25000, "Description": ".75% Rate Discount"}, {"DiscountPercentage": 1.0, "PointsRequired": 35000, "Description": "1.00% Rate Discount"}], "FixedCostRewards": [{"Type": "StopPayment", "PointsRequired": 2500, "Description": "Stop Payment Fee Waiver"}, {"Type": "SafeDepositBox", "PointsRequired": 5000, "Description": "$50 Safe Deposit Box Rental Credit"}, {"Type": "CDRate", "PointsRequired": 7000, "Description": "Share Certificate Increase (New Certs Only) (0.55%)"}, {"Type": "MortgageClosingCost", "PointsRequired": 10000, "Description": "$100 Mortgage Closing Cost Discount"}, {"Type": "HomeEquityClosingCost", "PointsRequired": 10000, "Description": "$161 Home Equity Closing Cost Discount"}, {"Type": "MortgageClosingCost", "PointsRequired": 25000, "Description": "$250 Mortgage Closing Cost Discount"}]}, "UChooseDisplayOptions": {"ShowPointsToDate": false, "ShowAvailablePoints": true, "ShowVestedPoints": true, "ShowCardName": false, "ShowCardStatus": false, "ShowCreditOrDebit": false, "ShowCardDescription": true, "ShowTotal": true, "ShowDetails": true}, "ConnectionStrings": {"DefaultConnection": "Data Source=(localdb)\\MSSQLLocalDB;Integrated Security=True;Connect Timeout=60;Encrypt=True;Trust Server Certificate=False;Application Intent=ReadWrite;Multi Subnet Failover=False"}}