# Variables
$AppPoolName = "SymitarTools"
$SitePath = "C:\inetpub\SymitarTools\"
$BackupPath = "C:\Backups\SymitarTools_$(Get-Date -Format 'yyyyMMddHHmmss')"
$ArtifactPath = "$PSScriptRoot\..\MVCApp\bin\Release\net8.0\publish"
Write-Host "Artifact path: $ArtifactPath"

# Stop app pool
Write-Host "Stopping IIS Application Pool: $AppPoolName"
Stop-WebAppPool -Name $AppPoolName

# Wait for the app pool to fully stop
Write-Host "Waiting for application pool to stop..."
Start-Sleep -Seconds 5

# Backup Existing Deployment
Write-Host "Backing up existing deployment to $BackupPath"
New-Item -ItemType Directory -Force -Path $BackupPath
Copy-Item -Path "$SitePath\*" -Destination $BackupPath -Recurse -Force

# Remove existing files
Write-Host "Clearing old files..."
$maxRetries = 5
$retryCount = 0
$success = $false

while (-not $success -and $retryCount -lt $maxRetries) {
    try {
        Remove-Item -Path "$SitePath\*" -Recurse -Force -ErrorAction Stop
        $success = $true
    } catch {
        Write-Host "Failed to delete files, retrying in 2 seconds... ($retryCount/$maxRetries)"
        Start-Sleep -Seconds 2
        $retryCount++
    }
}

if (-not $success) {
    Write-Host "Failed to remove files after $maxRetries retries. Exiting."
    Exit 1
}

# Deploy new files
Write-Host "Deploying new files from $ArtifactPath to $SitePath"
Copy-Item -Path "$ArtifactPath\*" -Destination $SitePath -Recurse -Force

# Start IIS App Pool
Write-Host "Starting IIS Application Pool: $AppPoolName"
Start-WebAppPool -Name $AppPoolName

# Verify Deployment
Write-Host "Deployment complete. Verifying..."
Invoke-WebRequest -Uri "http://localhost" -UseBasicParsing | Out-Null
if ($?) {
    Write-Host "Application is running successfully!"
} else {
    Write-Host "Application failed to start. Check IIS logs for details."
    Exit 1
}
