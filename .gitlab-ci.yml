variables:
  NUGET_PATH: C:\Tools\Nuget\nuget.exe
  GIT_STRATEGY: clone # fixes issue with older git installed on redhat
  SCAN_PATH: "$CI_PROJECT_DIR"
  TESTCIAPP_BUILD_NUMBER: "$CI_PIPELINE_ID"

  PUBLISH_PROJECT: 'MVCApp\MVCApp.csproj'
  MSBUILD_PATH: C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\amd64\MSBuild.exe
  PUBLISH_OUTPUT_DIR: '.\bin\Release\net8.0\publish\'
stages:
  - build
  - publish
  - deploy

build_job:
  stage: build
  when: always
  tags:
    - dotnet
    - build
  rules:
    - if: '$CI_PIPELINE_SOURCE == "push"'
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
  script:
    - echo "Restoring Nuget packages..."
    - dotnet restore ECU.Episys.Tools.sln
    - echo "Building solution using MSBuild..."
    - "& $env:MSBUILD_PATH /p:Configuration=Release"
  artifacts:
    paths:
      - "**/bin/Release/net8.0"
    expire_in: 2 days

publish_job:
  stage: publish
  tags:
    - dotnet
    - build
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
    - if: '$CI_COMMIT_BRANCH == "staging"'
  script:
    - echo "Restoring Nuget packages for solution..."
    - dotnet restore ECU.Episys.Tools.sln
    - echo "Publishing project using MSBuild..."
    - "& $env:MSBUILD_PATH $env:PUBLISH_PROJECT /p:Configuration=Release /p:DeployOnBuild=true /p:PublishDir=$env:PUBLISH_OUTPUT_DIR /p:SelfContained=false"
  artifacts:
    paths:
      - "**/bin/Release/net8.0/publish"
      - "scripts/deploy-script.ps1"
    expire_in: 2 days
  dependencies:
    - build_job

deploy_staging:
  stage: deploy
  tags:
    - dotnet
    - staging
  variables:
    GIT_STRATEGY: none
  rules:
    - if: '$CI_COMMIT_BRANCH == "staging"'
  script:
    - echo "Deploying to Staging on d1isapp-d01"
    - powershell -File ./scripts/deploy-script.ps1
  dependencies:
    - publish_job
  environment:
    name: staging
    url: https://d1isapp-d01.ecu.com/SymitarTools

deploy_production:
  stage: deploy
  tags:
    - dotnet
    - production
  variables:
    GIT_STRATEGY: none
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
  script:
    - echo "Deploying to Production on d1isapp-p02"
    - powershell -File ./scripts/deploy-script.ps1
  dependencies:
    - publish_job
  environment:
    name: production
    url: https://d1isapp-p02.ecu.com/SymitarTools
