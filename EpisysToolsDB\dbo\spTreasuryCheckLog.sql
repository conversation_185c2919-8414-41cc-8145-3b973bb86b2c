﻿CREATE PROCEDURE [dbo].[spTreasuryCheckLog]
	@UserId int,
	@SymbolNumber varchar(50),
	@SerialNumber varchar(50),
	@Amount decimal(18,2),
	@IsValid bit,
	@Error varchar(50),
	@Payee nvarchar(50)
	

AS
BEGIN
	SET NOCOUNT ON;

	INSERT INTO TreasuryCheckUsers(SymbolNumber,SerialNumber,Amount,IsValid,AttemptedAt,UserId,Error,Payee)
	VALUES(
		@SymbolNumber,
		@SerialNumber,
		@Amount,
		@IsValid,
		GETDATE(),
		@UserId,
		@Error,
		@Payee
		);
END



