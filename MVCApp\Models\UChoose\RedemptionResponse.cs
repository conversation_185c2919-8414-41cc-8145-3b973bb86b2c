using System.Net;

namespace MVCApp.Models.UChoose
{
    /// <summary>
    /// Response model for redemption operations
    /// </summary>
    public class RedemptionResponse
    {
        /// <summary>
        /// HTTP status code of the redemption operation
        /// </summary>
        public HttpStatusCode StatusCode { get; set; }

        /// <summary>
        /// Indicates if the points redemption was successful
        /// </summary>
        public bool PointsRedemptionSuccessful { get; set; }

        /// <summary>
        /// Indicates if the note creation was successful (if applicable)
        /// </summary>
        public bool? NoteCreationSuccessful { get; set; }

        /// <summary>
        /// Error message if note creation failed
        /// </summary>
        public string NoteCreationError { get; set; }

        /// <summary>
        /// The redemption type that was processed
        /// </summary>
        public string RedemptionType { get; set; }

        /// <summary>
        /// Creates a successful response
        /// </summary>
        public static RedemptionResponse Success(string redemptionType, bool? noteCreationSuccessful = null, string noteCreationError = null)
        {
            return new RedemptionResponse
            {
                StatusCode = HttpStatusCode.OK,
                PointsRedemptionSuccessful = true,
                NoteCreationSuccessful = noteCreationSuccessful,
                NoteCreationError = noteCreationError,
                RedemptionType = redemptionType
            };
        }

        /// <summary>
        /// Creates a failed response
        /// </summary>
        public static RedemptionResponse Failure(HttpStatusCode statusCode, string redemptionType = null)
        {
            return new RedemptionResponse
            {
                StatusCode = statusCode,
                PointsRedemptionSuccessful = false,
                NoteCreationSuccessful = null,
                NoteCreationError = null,
                RedemptionType = redemptionType
            };
        }
    }
}
