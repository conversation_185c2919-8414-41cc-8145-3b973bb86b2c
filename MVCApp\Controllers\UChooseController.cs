using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MVCApp.Helpers;
using MVCApp.Models.UChoose;
using MVCApp.ViewModels;
using MVCApp.ViewModels.uChoose;
using Newtonsoft.Json;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Threading.Tasks;
using static MVCApp.Models.UChoose.UChooseOptionsModel;
using RestSharp;
using SymXchangeAccountWSDL;

namespace MVCApp.Controllers
{
    public class UChooseController : Controller
    {
        private readonly IConfiguration _configuration;
        private readonly UChooseHelper _UChooseHelper;
        private readonly SymXchangeAccountHelper _symxAccountHelper;
        private readonly ILogger<UChooseController> _logger;
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly UChooseRewardsConfig _rewardsConfig;


        public UChooseController(
            ILogger<UChooseController> logger,
            IConfiguration configuration,
            IWebHostEnvironment webHostEnvironment,
            IOptions<UChooseRewardsConfig> rewardsConfig,
            SymXchangeAccountHelper symxAccountHelper,
            UChooseHelper uChooseHelper)
        {
            _logger = logger;
            _configuration = configuration;
            _UChooseHelper = uChooseHelper;
            _symxAccountHelper = symxAccountHelper;
            _webHostEnvironment = webHostEnvironment;
            _rewardsConfig = rewardsConfig.Value;
        }

        public async Task<IActionResult> Index()
        {

            var options = new UChooseOptionsModel();
            var redemptionOptionsFile = Path.Combine(_webHostEnvironment.WebRootPath, "data", "redemptionOptions.json");
            using StreamReader reader = new(redemptionOptionsFile);
            var json = reader.ReadToEnd();
            var redemptionOptions = JsonConvert.DeserializeObject<UChooseOptionsModel>(json);

            //TODO: Load page skeleton then call get data in ajax so we can show a loadin spinner
            var model = new UChooseViewModel();
            model.CardPoints = new List<PointsModel>();
            string appKey;
            List<string> cardNums = new List<string>();
            short userNum;
            string accountNum;

#if DEBUG
            model.UserName = "EDUCATORS CREDIT UNION";
           cardNums.Add("35|****************|DEBIT");
           cardNums.Add("92|****************|PLATINUM CREDIT");
           appKey = "173495AF-BB12-4712-8027-FE500ED5CEC5";
           userNum = 1741;
           accountNum = "710664";
#else
            model.UserName = HttpContext.Request.Form["UserName"];
            cardNums = HttpContext.Request.Form["CardNums"].ToList();
            appKey = HttpContext.Request.Form["AppKey"];
            userNum = Convert.ToInt16(HttpContext.Request.Form["UserNumber"]);
            accountNum = HttpContext.Request.Form["AccountNumber"];
#endif

            if (appKey != _configuration["AppKey"])
            {
                return View("Error", new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
            }
            //Do we want to move this so it is called within the methods and not passed to the methods?
             var apiToken = await _UChooseHelper.GetToken();

            // TODO: When refacotring for multi account ownership, this will need updated
            var loansResponse = await _symxAccountHelper.GetLoansByAccountNumber(accountNum, userNum);
            
            foreach (var card in cardNums)
            {
                // card will be CardType|CardNumber|CardDescription
                // TODO: Change to pass in json complex data?
                var cardData = card.Split('|');
                var pointsResponse = await _UChooseHelper.GetPointsByCardNumber(Convert.ToInt32(cardData[0]), cardData[1]);

                if (pointsResponse.PointsInfo != null)
                {
                    var pointsModel = new PointsModel()
                    {
                        PointsToDate = (int)pointsResponse.PointsInfo.pointsToDate,
                        AvailablePoints = (int)pointsResponse.PointsInfo.availablePoints,
                        VestedPoints = (int)pointsResponse.PointsInfo.vestedPoints,
                        CardName = pointsResponse.PointsInfo.ccName,
                        CardStatus = (bool)pointsResponse.PointsInfo.ccStatus,
                        CreditOrDebit = Convert.ToInt32(cardData[0]) <= 40 ? CreditOrDebit.Debit : CreditOrDebit.Credit,
                        CardDescription = cardData[2],
                        CardNumber = cardData[1],
                        CardType = Convert.ToInt32(cardData[0])

                    };
                    model.CardPoints.Add(pointsModel);
                }
            }

            model.TotalPointsToDate = model.CardPoints.Sum(item => item.PointsToDate);
            model.TotalAvailablePoints = model.CardPoints.Sum(item => item.AvailablePoints);
            model.TotalVestedPoints = model.CardPoints.Sum(item => item.VestedPoints);

            // Populate loans data
            model.Loans = new List<LoanModel>();
            try
            {
                var loanArray = loansResponse.PagedResponse.Loan;
                if (loanArray != null && loanArray.Any())
                {
                    foreach (var loan in loanArray)
                    {
                        model.Loans.Add(new LoanModel
                        {
                            AccountNumber = accountNum,
                            Id = loan.Id,
                            Description = loan.Description
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Could not parse loan response structure. Loans will not be available for selection.");
            }

            return View(model);
        }

        [HttpPost]
        public async Task<HttpStatusCode> RedeemPoints(int cardType, string cardNum, int points, string redeemOption, string loanId = null, string accountNumber = null)
        {
            _logger.LogInformation("Processing redemption with type: {redeemOption}", redeemOption);

            string rewardDescription = null;

            // Parse the redemption type if it's provided
            if (!string.IsNullOrEmpty(redeemOption) && Enum.TryParse(redeemOption, out RedemptionType redemptionType))
            {
                _logger.LogInformation("Redemption type parsed: {redemptionType}", redemptionType);

                // Handle different redemption types
                switch (redemptionType)
                {
                    case RedemptionType.LoanRateDiscount:
                        // Logic for loan rate discount
                        _logger.LogInformation("Processing loan rate discount");

                        // Validate that a loan ID was provided
                        if (string.IsNullOrEmpty(loanId))
                        {
                            _logger.LogWarning("Loan ID is required for loan rate discount redemption");
                            return HttpStatusCode.BadRequest;
                        }

                        // Find the matching discount tier
                        var matchingTier = _rewardsConfig.LoanRateDiscounts.FirstOrDefault(t => t.PointsRequired == points);
                        if (matchingTier == null)
                        {
                            _logger.LogWarning("No matching loan rate discount tier found for {points} points", points);
                            return HttpStatusCode.BadRequest;
                        }

                        _logger.LogInformation("Applying {discount}% loan rate discount requiring {points} points to loan {loanId}",
                            matchingTier.DiscountPercentage, matchingTier.PointsRequired, loanId);

                        // Set the reward description
                        rewardDescription = matchingTier.Description;
                        break;

                    case RedemptionType.SafeDepositBox:
                    case RedemptionType.StopPayment:
                    case RedemptionType.CDRate:
                    case RedemptionType.MortgageClosingCost:
                    case RedemptionType.HomeEquityClosingCost:
                        // Logic for fixed-cost rewards
                        var rewardConfig = _rewardsConfig.FixedCostRewards.FirstOrDefault(r =>
                            r.Type.Equals(redemptionType.ToString(), StringComparison.OrdinalIgnoreCase));

                        if (rewardConfig == null)
                        {
                            _logger.LogWarning("No configuration found for redemption type: {redemptionType}", redemptionType);
                            return HttpStatusCode.BadRequest;
                        }

                        if (points != rewardConfig.PointsRequired)
                        {
                            _logger.LogWarning("Incorrect points for {redemptionType}. Required: {required}, Provided: {provided}",
                                redemptionType, rewardConfig.PointsRequired, points);
                            return HttpStatusCode.BadRequest;
                        }

                        _logger.LogInformation("Processing {description} for {points} points",
                            rewardConfig.Description, rewardConfig.PointsRequired);

                        // Set the reward description
                        rewardDescription = rewardConfig.Description;
                        break;

                    default:
                        _logger.LogWarning("Unknown redemption type: {redemptionType}", redemptionType);
                        return HttpStatusCode.BadRequest;
                }
            }
            else
            {
                _logger.LogWarning("Could not parse redemption type from: {redeemOption}", redeemOption);
                return HttpStatusCode.BadRequest;
            }

            // Call the UChooseHelper to redeem points with the reward description
#if DEBUG
            var response = HttpStatusCode.OK; // Simulating a successful response for now
#else
            var response = await _UChooseHelper.RedeemPoints(cardType, cardNum, points, redeemOption, rewardDescription);
#endif
            
            // If this was a loan rate discount redemption and the points redemption was successful, create a loan note
            if (response == HttpStatusCode.OK && redemptionType == RedemptionType.LoanRateDiscount && !string.IsNullOrEmpty(loanId))
            {
                try
                {
                    // Get user number from the current context
                    short userNum = 1741; // Default for debug

                    // Create a loan note documenting the rate discount redemption
                    var noteText = $"{points} REDEEMED FOR {rewardDescription} FROM ACCT# {accountNumber}";

                    var loanNoteResponse = await _symxAccountHelper.CreateLoanNote(accountNumber, loanId, userNum, noteText);

                    if (loanNoteResponse != null)
                    {
                        _logger.LogInformation("Successfully created loan note for loan rate discount redemption on loan {loanId}", loanId);
                    }
                    else
                    {
                        _logger.LogWarning("Failed to create loan note for loan rate discount redemption on loan {loanId}", loanId);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating loan note for loan rate discount redemption on loan {loanId}", loanId);
                    // Don't fail the entire redemption if loan note creation fails
                }
            }

            return response;
        }

        [HttpPost]
        public async Task<HttpStatusCode> TransferPoints(int receivingCardType, string receivingCardNumber, int burnCardType, string burnCardNumber, int points)
        {
            _logger.LogInformation("Processing points transfer: From Card Type: {burnCardType}, Account: {burnAccount} To Card Type: {receivingCardType}, Account: {receivingAccount}, Points: {points}",
                burnCardType, burnCardNumber, receivingCardType, receivingCardNumber, points);

            try
            {
                // Validate inputs
                if (string.IsNullOrEmpty(burnCardNumber) || string.IsNullOrEmpty(receivingCardNumber))
                {
                    _logger.LogError("Card numbers cannot be null or empty");
                    return HttpStatusCode.BadRequest;
                }

                if (points <= 0)
                {
                    _logger.LogError("Points must be greater than zero. Received: {points}", points);
                    return HttpStatusCode.BadRequest;
                }

                // Call the helper method to transfer points
                var response = await _UChooseHelper.TransferPoints(receivingCardType, receivingCardNumber, burnCardType, burnCardNumber, points);

                if (response == HttpStatusCode.OK)
                {
                    _logger.LogInformation("Successfully transferred {points} points from card {burnCard} to card {receivingCard}",
                        points, burnCardNumber, receivingCardNumber);
                    return HttpStatusCode.OK;
                }
                else
                {
                    _logger.LogWarning("Failed to transfer points. Status: {status}", response);
                    return response;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error transferring points from card {burnCard} to card {receivingCard}",
                    burnCardNumber, receivingCardNumber);
                return HttpStatusCode.InternalServerError;
            }
        }

        [HttpPost]
        public async Task<HttpStatusCode> AddPoints(int cardType, string cardNumber, int points)
        {
            _logger.LogInformation("Adding points: Card Type: {cardType}, Card Number: {cardNumber}, Points: {points}",
                cardType, cardNumber, points);

            try
            {
                if (string.IsNullOrEmpty(cardNumber))
                {
                    _logger.LogError("Card number is null or empty");
                    return HttpStatusCode.BadRequest;
                }

                if (points <= 0)
                {
                    _logger.LogError("Points must be greater than zero. Received: {points}", points);
                    return HttpStatusCode.BadRequest;
                }

                var apiToken = await _UChooseHelper.GetToken();
                var response = await _UChooseHelper.AddPoints(apiToken, cardType, cardNumber, points);

                if (response.IsSuccessful)
                {
                    _logger.LogInformation("Successfully added {points} points to card {cardNumber}", points, cardNumber);
                    return HttpStatusCode.OK;
                }
                else
                {
                    _logger.LogWarning("Failed to add points. Status: {status}, Response: {response}",
                        response.StatusCode, response.Content);
                    return response.StatusCode;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding points to card {cardNumber}", cardNumber);
                return HttpStatusCode.InternalServerError;
            }
        }

        [HttpGet]
        public async Task<ActionResult> GetCardData(int cardType, string cardNumber, string cardDescription)
        {
            _logger.LogInformation("Fetching card data: Card Type: {cardType}, Card Number: {cardNumber}",
                cardType, cardNumber);

            try
            {
                if (string.IsNullOrEmpty(cardNumber))
                {
                    _logger.LogError("Card number is null or empty");
                    return BadRequest("Card number is required");
                }

                // Get points data for the card
                var pointsResponse = await _UChooseHelper.GetPointsByCardNumber(cardType, cardNumber);

                if (pointsResponse.PointsInfo == null)
                {
                    _logger.LogWarning("No points information found for card {cardNumber}", cardNumber);
                    return NotFound($"No data found for card {cardNumber}");
                }

                // Create a PointsModel from the response
                var pointsModel = new PointsModel()
                {
                    PointsToDate = pointsResponse.PointsInfo.pointsToDate,
                    AvailablePoints = pointsResponse.PointsInfo.availablePoints,
                    VestedPoints = pointsResponse.PointsInfo.vestedPoints,
                    CardName = pointsResponse.PointsInfo.ccName,
                    CardStatus = pointsResponse.PointsInfo.ccStatus,
                    CreditOrDebit = cardType <= 40 ? CreditOrDebit.Debit : CreditOrDebit.Credit,
                    CardDescription = cardDescription,
                    CardNumber = cardNumber,
                    CardType = cardType
                };

                return Json(pointsModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching card data for card {cardNumber}", cardNumber);
                return StatusCode(500, "An error occurred while fetching card data");
            }
        }
    }
}
