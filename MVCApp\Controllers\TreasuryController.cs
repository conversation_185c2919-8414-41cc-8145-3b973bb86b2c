﻿using EpisysToolsClassLibrary.Data;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
using System;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using X.PagedList;
using X.PagedList.Extensions;

namespace MVCApp.Controllers
{
    public class TreasuryController : Controller
    {
        private readonly ITreasuryLog _treasuryLog;
        private readonly string _apiBaseUrl = "https://tcvs.fiscal.treasury.gov";
        private readonly string _apiKey;
        private readonly string _bankRtn = "*********";

        public TreasuryController(IConfiguration configuration, ITreasuryLog treasuryLog)
        {
            _apiKey = configuration.GetValue<string>("TreasuryKey");
            _treasuryLog = treasuryLog;
        }

        [HttpGet]
        public IActionResult CheckVerification(int appKey, int userId)
        {
            ViewBag.AppKey = appKey;
            ViewBag.UserId = userId;
            HttpContext.Session.SetInt32("UserId", userId);

            return View();
        }

        [HttpPost]
        public async Task<IActionResult> CheckVerification(int appKey, string symbolNumber, string serialNumber, decimal amount)
        {
            TempData["symbolNumber"] = symbolNumber;
            TempData["serialNumber"] = serialNumber;
            TempData["amount"] = amount;
            var userId = HttpContext.Session.GetInt32("UserId");
           

            if (string.IsNullOrEmpty(symbolNumber) || string.IsNullOrEmpty(serialNumber) || amount <= 0)
            { return View(); }
            var client = new RestClient(_apiBaseUrl);
            var request = new RestRequest($"/api/v2/validate?symbol_number={symbolNumber}&serial_number={serialNumber}&amount={amount}&bank_rtn={_bankRtn}", Method.Get);

            request.AddHeader("Accept", "application/json");
            request.AddHeader("Ocp-Apim-Subscription-Key", _apiKey);

            RestResponse response = await client.ExecuteAsync(request);

            ViewData["ApiResponse"] = response.Content ?? "No response from API.";

            var apiResponse = JsonConvert.DeserializeObject<JObject>(response.Content);
            bool symbolsMatch = true;
            bool amountMatch = true;
            var payeeValue = "";
            if (apiResponse["checks"] is JArray checksArray && checksArray.Count > 0)
            {
                var firstCheck = checksArray[0];

                payeeValue = firstCheck["payee"]?.ToString() ?? "Payee does not exist";

                if (firstCheck["symbol_serial_matches"]?.ToObject<bool>() == false)
                {
                    symbolsMatch = false;
                }

                if (firstCheck["amount_matches"]?.ToObject<bool>() == false)
                {
                    amountMatch = false;

                }
                
            }
            var error = "";
            var isValid = true;
            if (symbolsMatch == false && amountMatch == false)
            {
                error = "Symbol_Serial and Amount Mismatch";
                isValid = false;
            }   
            else if (amountMatch == false) {
                error = "Amount Mismatch";
                isValid = false;
            } else if (symbolsMatch == false) {
                error = "Symbol_Serial Mismatch";
                isValid = false;
            }

            await _treasuryLog.CreateTreasuryLog(userId.Value, symbolNumber, serialNumber, amount, isValid, error, payeeValue );
            return View();
        }

        [HttpGet]
        public async Task<IActionResult> TreasuryLog(
            string sortOrder,
            string currentFiler,
            string searchString,
            int? page,
            int? pageSize,
            int? userId,
            DateTime? date)
        {
            if(searchString != null)
            {
                page = 1;
            }
            else
            {
                searchString = currentFiler;
            }

            ViewBag.CurrentFilter = searchString;
            ViewBag.CurrentSort = sortOrder;

            ViewBag.DateSortParam = sortOrder == "Date" ? "date_desc" : "Date";
            ViewBag.UserSortParam = sortOrder == "User" ? "user_desc" : "User";
            ViewBag.ValidSortParam = sortOrder == "Valid" ? "valid_desc" : "Valid";

            var logs = await _treasuryLog.GetTreasuryLogs();

            //filter
            if (!string.IsNullOrEmpty(searchString))
            {
                logs = logs.Where(x =>
                x.SymbolNumber.Contains(searchString, StringComparison.OrdinalIgnoreCase) ||
                x.SerialNumber.Contains(searchString, StringComparison.OrdinalIgnoreCase) ||
                x.Error?.Contains(searchString, StringComparison.OrdinalIgnoreCase) == true ||
                x.Payee?.Contains(searchString, StringComparison.OrdinalIgnoreCase) == true)
                .ToList();
            }

            if (userId.HasValue)
            {
                logs = logs.Where(x => x.UserId == userId.Value).ToList();
            }

            if (date.HasValue)
            {
                logs = logs.Where(x => x.AttemptedAt.Date == date.Value.Date).ToList();
            }

            //sort
            logs = sortOrder switch
            {
                "data_desc" => logs.OrderByDescending(x => x.AttemptedAt).ToList(),
                "Date" => logs.OrderBy(x => x.AttemptedAt).ToList(),
                "user_desc" => logs.OrderByDescending(x => x.UserId).ToList(),
                "User" => logs.OrderByDescending(x => x.UserId).ToList(),
                "valid_desc" => logs.OrderByDescending(x => x.IsValid).ToList(),
                "Valid" => logs.OrderByDescending(x => x.IsValid).ToList(),
                _ => logs.OrderByDescending(x => x.AttemptedAt).ToList()
            };

            int actualPageSize = pageSize ?? 10;
            ViewBag.CurrentPageSize = actualPageSize;

            var pagedLogs = logs.ToPagedList(page ?? 1, actualPageSize);
            return View(pagedLogs);
        }
    }
}
    