﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Version>2.0.1</Version>
    <UserSecretsId>c6945d64-4009-4417-8f77-37d80ca30482</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNet.WebApi.Client" Version="6.0.0" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="8.0.7" />
    <PackageReference Include="RestSharp" Version="112.1.0" />
    <PackageReference Include="System.ServiceModel.Http" Version="8.0.0" />
    <PackageReference Include="System.ServiceModel.Primitives" Version="8.0.0" />
    <PackageReference Include="X.PagedList.Mvc.Core" Version="10.5.7" />
    <PackageReference Include="Serilog.AspNetCore" Version="7.0.0" />
    <PackageReference Include="Serilog.Enrichers.Environment" Version="2.2.0" />
    <PackageReference Include="Serilog.Enrichers.Process" Version="2.0.2" />
    <PackageReference Include="Serilog.Enrichers.Thread" Version="3.1.0" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="7.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\EpisysToolsClassLibrary\EpisysToolsClassLibrary.csproj" />
    
  </ItemGroup>

  <ItemGroup>
    <Reference Include="SymXchangeWSDL">
      <HintPath>..\dependencies\SymXchangeWSDL.dll</HintPath>
    </Reference>
    <Reference Include="SymXchangeWSDL.XmlSerializers">
      <HintPath>..\dependencies\SymXchangeWSDL.XmlSerializers.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
